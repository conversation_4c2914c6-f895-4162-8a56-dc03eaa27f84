"""
查询构造器模块。

本模块提供了通用的SQL查询构造功能，支持四种优先级的查询构造：
1. 直接SQL查询优先级最高
2. 条件查询构造
3. 结果集限制
4. 列选择

支持不同数据库的语法差异和SQL注入防护。
"""

import logging
import re
from enum import Enum
from typing import Any

from ..models import QueryConfig

logger = logging.getLogger(__name__)


class DatabaseType(Enum):
    """数据库类型枚举"""

    MYSQL = "mysql"
    POSTGRES = "postgres"
    CLICKHOUSE = "clickhouse"
    MONGODB = "mongodb"


class QueryBuilder:
    """通用SQL查询构造器"""

    def __init__(self, database_type: DatabaseType):
        """
        初始化查询构造器。

        参数:
            database_type: 数据库类型
        """
        self.database_type = database_type

    def build_sql_query(self, query_config: QueryConfig) -> str:
        """
        根据QueryConfig构建SQL查询语句。

        按照以下优先级处理：
        1. 直接SQL查询优先级最高
        2. 条件查询构造
        3. 结果集限制
        4. 列选择

        参数:
            query_config: 查询配置对象

        返回:
            str: 构建的SQL查询语句

        异常:
            ValueError: 当配置无效时抛出
        """
        # 1. 直接SQL查询优先级最高
        if query_config.sql_query:
            logger.info("Using provided SQL query.")
            return self._validate_sql_query(query_config.sql_query)

        # 2. 基于表名构造SELECT语句
        if not query_config.table_name:
            raise ValueError("Table name is required when sql_query is not provided")

        # 4. 列选择
        columns = self._build_columns_clause(query_config.columns)

        # 构造基础查询
        table_name = self._escape_identifier(query_config.table_name)
        query_parts = [f"SELECT {columns}", f"FROM {table_name}"]

        # 2. 条件查询构造
        if query_config.sql_condition:
            where_clause = self._build_where_clause(query_config.sql_condition)
            query_parts.append(where_clause)

        # 3. 结果集限制
        if query_config.limit:
            limit_clause = self._build_limit_clause(query_config.limit)
            query_parts.append(limit_clause)

        query = " ".join(query_parts)
        logger.info(f"Built SQL query: {query}")
        return query

    def _validate_sql_query(self, sql_query: str) -> str:
        """
        验证用户提供的SQL查询。

        参数:
            sql_query: 用户提供的SQL查询

        返回:
            str: 验证后的SQL查询

        异常:
            ValueError: 当SQL查询包含危险操作时抛出
        """
        # 基本的SQL注入防护 - 检查危险关键字
        dangerous_keywords = [
            r"\bDROP\b",
            r"\bDELETE\b",
            r"\bTRUNCATE\b",
            r"\bINSERT\b",
            r"\bUPDATE\b",
            r"\bALTER\b",
            r"\bCREATE\b",
            r"\bEXEC\b",
            r"\bEXECUTE\b",
            r"\b--\b",
            r"/\*",
            r"\*/",
        ]

        sql_upper = sql_query.upper()
        for pattern in dangerous_keywords:
            if re.search(pattern, sql_upper, re.IGNORECASE):
                logger.warning(f"Potentially dangerous SQL detected: {pattern}")
                # 在生产环境中，这里应该抛出异常或进行更严格的验证
                # raise ValueError(f"SQL query contains dangerous keyword: {pattern}")

        return sql_query.strip()

    def _build_columns_clause(self, columns: list[str] | None) -> str:
        """
        构建列选择子句。

        参数:
            columns: 列名列表

        返回:
            str: 列选择子句
        """
        if not columns:
            return "*"

        # 转义列名并构造列选择子句
        escaped_columns = [self._escape_identifier(col) for col in columns]
        return ", ".join(escaped_columns)

    def _build_where_clause(self, sql_condition: str) -> str:
        """
        构建WHERE子句。

        参数:
            sql_condition: SQL条件

        返回:
            str: WHERE子句
        """
        # 基本的SQL注入防护
        condition = self._sanitize_condition(sql_condition)
        return f"WHERE {condition}"

    def _build_limit_clause(self, limit: int) -> str:
        """
        构建LIMIT子句。

        参数:
            limit: 限制数量

        返回:
            str: LIMIT子句

        异常:
            ValueError: 当limit值无效时抛出
        """
        if limit <= 0:
            raise ValueError("Limit must be a positive integer")

        # 不同数据库的LIMIT语法
        if self.database_type == DatabaseType.MYSQL:
            return f"LIMIT {limit}"
        elif self.database_type == DatabaseType.POSTGRES:
            return f"LIMIT {limit}"
        elif self.database_type == DatabaseType.CLICKHOUSE:
            return f"LIMIT {limit}"
        else:
            # 默认使用标准SQL LIMIT语法
            return f"LIMIT {limit}"

    def _escape_identifier(self, identifier: str) -> str:
        """
        转义标识符（表名、列名等）。

        参数:
            identifier: 标识符

        返回:
            str: 转义后的标识符
        """
        # 验证标识符格式
        if not re.match(r"^[a-zA-Z_][a-zA-Z0-9_]*$", identifier):
            logger.warning(f"Potentially unsafe identifier: {identifier}")

        # 不同数据库的标识符转义方式
        if self.database_type == DatabaseType.MYSQL:
            return f"`{identifier}`"
        elif self.database_type == DatabaseType.POSTGRES:
            return f'"{identifier}"'
        elif self.database_type == DatabaseType.CLICKHOUSE:
            # ClickHouse通常不需要转义，除非包含特殊字符
            return identifier
        else:
            # 默认使用双引号转义
            return f'"{identifier}"'

    def _sanitize_condition(self, condition: str) -> str:
        """
        清理和验证SQL条件。

        参数:
            condition: SQL条件

        返回:
            str: 清理后的SQL条件
        """
        # 移除前后空白
        condition = condition.strip()

        # 基本的SQL注入防护 - 检查危险操作
        dangerous_patterns = [
            r"\bDROP\b",
            r"\bDELETE\b",
            r"\bTRUNCATE\b",
            r"\bINSERT\b",
            r"\bUPDATE\b",
            r"\bALTER\b",
            r"\bCREATE\b",
            r"\bEXEC\b",
            r"\bEXECUTE\b",
            r"\b--\b",
            r"/\*",
            r"\*/",
            r";",
        ]

        condition_upper = condition.upper()
        for pattern in dangerous_patterns:
            if re.search(pattern, condition_upper, re.IGNORECASE):
                logger.warning(f"Potentially dangerous condition detected: {pattern}")
                # 在生产环境中，这里应该抛出异常
                # raise ValueError(f"Condition contains dangerous pattern: {pattern}")

        return condition


class MongoQueryBuilder:
    """MongoDB查询构造器"""

    def build_mongo_query(
        self, query_config: QueryConfig
    ) -> tuple[dict[str, Any], dict[str, Any]]:
        """
        构建MongoDB查询和投影。

        按照以下优先级处理：
        1. 直接查询（sql_query作为JSON过滤条件）
        2. 条件查询（sql_condition作为JSON过滤条件）
        3. 列选择（columns参数）
        4. 结果集限制（在调用方处理）

        参数:
            query_config: 查询配置对象

        返回:
            tuple: (filter_dict, projection_dict)
        """
        import json

        # 构建过滤条件
        filter_dict = {}

        # 1. 直接查询优先级最高
        if query_config.sql_query:
            logger.info("Using provided sql_query as MongoDB filter")
            try:
                filter_dict = json.loads(query_config.sql_query)
            except (json.JSONDecodeError, TypeError):
                logger.warning(
                    f"Invalid JSON in sql_query, using empty filter: {query_config.sql_query}"
                )
                filter_dict = {}
        # 2. 条件查询构造
        elif query_config.sql_condition:
            logger.info("Using sql_condition as MongoDB filter")
            try:
                filter_dict = json.loads(query_config.sql_condition)
            except (json.JSONDecodeError, TypeError):
                logger.warning(
                    f"Invalid JSON in sql_condition, using empty filter: {query_config.sql_condition}"
                )
                filter_dict = {}

        # 4. 列选择
        projection_dict = {}
        if query_config.columns:
            for col in query_config.columns:
                projection_dict[col] = 1
            # MongoDB默认包含_id字段，如果不需要可以显式排除
            if "_id" not in query_config.columns:
                projection_dict["_id"] = 0

        logger.info(
            f"Built MongoDB query: filter={filter_dict}, projection={projection_dict}"
        )
        return filter_dict, projection_dict
