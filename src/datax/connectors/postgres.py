"""
PostgreSQL 连接器模块。

本模块提供了 PostgreSQL 数据库的提取器和加载器实现。
支持异步操作，使用 asyncpg 库进行高性能数据处理。
提供了完整的连接管理、查询构建和批量数据加载功能。

主要功能：
- PostgresConnector: 共享的连接管理基类
- PostgresExtractor: 流式数据提取器，支持游标分批读取
- PostgresLoader: 批量数据加载器，支持 ON CONFLICT 冲突处理
- 支持自定义初始化 SQL 命令
- 支持 COPY FROM 高性能批量导入
"""

import logging
from collections.abc import AsyncIterator
from typing import Any, override

import asyncpg  # type: ignore
import pandas as pd
from asyncpg import Connection as PgConnection

from ..core import Extractor, Loader, MetadataProvider
from ..core.metadata import CachedMetadataProvider
from ..models import (
    ColumnInfo,
    ColumnType,
    ConflictStrategy,
    ConstraintInfo,
    ConstraintType,
    ExportConfig,
    ImportConfig,
    IndexInfo,
    IndexType,
    PostgresConfig,
    QueryConfig,
    TableMetadata,
)
from .query_builder import DatabaseType, QueryBuilder

logger = logging.getLogger(__name__)


class PostgresConnector:
    """PostgreSQL 共享连接逻辑。"""

    def __init__(self, config: PostgresConfig):
        self.config = config
        self.connection: PgConnection | None = None

    async def _connect(self) -> None:
        """建立并配置 PostgreSQL 连接。"""
        if self.connection and not self.connection.is_closed():
            return

        try:
            # 构建连接参数
            connection_params = {
                "database": self.config.database,
                "user": self.config.username,
                "password": self.config.get_password_value(),
                "host": self.config.host,
                "port": self.config.port,
            }

            # 如果有init_sql，添加到连接参数中
            if self.config.init_sql:
                connection_params["command_timeout"] = 60  # 设置命令超时
                connection_params["server_settings"] = {
                    "application_name": "datax-ETL",
                    "client_encoding": "UTF8",
                }

            self.connection = await asyncpg.connect(**connection_params)

            # 如果有init_sql，在连接建立后执行
            if self.config.init_sql:
                await self.connection.execute(self.config.init_sql)  # type:ignore
                logger.info(f"Executed init_sql: {self.config.init_sql}")

            logger.debug("PostgreSQL connection established.")

        except asyncpg.PostgresError as e:
            logger.error(f"PostgreSQL connection failed: {e}")
            raise

    async def close(self) -> None:
        """关闭连接。"""
        if self.connection:
            try:
                await self.connection.close()
                self.connection = None
                logger.debug("PostgreSQL connection closed.")
            except asyncpg.PostgresError as e:
                logger.error(f"Error closing PostgreSQL connection: {e}")


class PostgresExtractor(PostgresConnector, Extractor):
    """PostgreSQL 数据提取器。"""

    def __init__(self, config: PostgresConfig):
        PostgresConnector.__init__(self, config)
        Extractor.__init__(self, config)
        self.query_builder = QueryBuilder(DatabaseType.POSTGRES)

    def _build_query(self, query_config: QueryConfig) -> str:
        """
        根据 QueryConfig 构建 SQL 查询。

        使用通用查询构造器，支持四种优先级的查询构造：
        1. 直接SQL查询优先级最高
        2. 条件查询构造（sql_condition参数）
        3. 结果集限制（limit参数）
        4. 列选择（columns参数）

        参数:
            query_config: 查询配置对象

        返回:
            str: 构建的 SQL 查询语句
        """
        return self.query_builder.build_sql_query(query_config)

    @override
    async def extract_stream(
        self, query_config: QueryConfig, export_config: ExportConfig
    ) -> AsyncIterator[pd.DataFrame]:
        """使用服务器端游标进行流式提取。"""
        await self._connect()
        if not self.connection:
            raise ConnectionError("PostgreSQL connection not established.")

        query = self._build_query(query_config)
        logger.info(f"Executing query: {query}")

        buffer: list[dict[str, Any]] = []
        total_rows_processed = 0

        try:
            # 使用 asyncpg 的 cursor 进行流式处理
            async with self.connection.transaction():
                async for record in self.connection.cursor(query):
                    # 将 record 转换为 dict
                    row_dict = dict(record)
                    buffer.append(row_dict)
                    total_rows_processed += 1

                    # 当缓冲区达到指定大小时，立即返回数据块
                    if len(buffer) >= export_config.parquet_chunk_size:
                        df = pd.DataFrame.from_records(buffer)
                        logger.info(
                            f"Yielding chunk: {len(buffer)} rows, total processed: {total_rows_processed}"
                        )
                        yield df
                        buffer.clear()

            # 处理剩余的数据
            if buffer:
                df = pd.DataFrame.from_records(buffer)
                logger.info(
                    f"Yielding final chunk: {len(buffer)} rows, total processed: {total_rows_processed}"
                )
                yield df

        finally:
            await self.close()


class PostgresLoader(PostgresConnector, Loader):
    """PostgreSQL 数据加载器。"""

    def __init__(self, config: PostgresConfig):
        PostgresConnector.__init__(self, config)
        Loader.__init__(self, config)

    @override
    async def load_chunk(self, data: pd.DataFrame, import_config: ImportConfig) -> None:
        """加载数据块到 PostgreSQL。"""
        await self._connect()
        if not self.connection or data.empty:
            return

        columns = ", ".join(f'"{col}"' for col in data.columns)

        # 使用带缓存的元数据查询获取冲突目标列
        base_provider = PostgresMetadataProvider(self.config)
        metadata_provider = CachedMetadataProvider(base_provider)
        try:
            table_metadata = await metadata_provider.get_table_metadata(
                import_config.table_name
            )

            # 获取主键列作为冲突目标
            conflict_target_columns = table_metadata.get_primary_key_columns()

            # 如果没有主键，尝试使用第一个唯一键
            if not conflict_target_columns:
                unique_keys = table_metadata.get_unique_key_columns()
                if unique_keys:
                    conflict_target_columns = unique_keys[0]

            conflict_target_str = (
                ", ".join(f'"{col}"' for col in conflict_target_columns)
                if conflict_target_columns
                else ""
            )

        except Exception as e:
            logger.warning(
                f"Failed to get table metadata for '{import_config.table_name}': {e}. Using ON CONFLICT DO NOTHING."
            )
            conflict_target_columns = []
            conflict_target_str = ""

        if (
            import_config.conflict_strategy == ConflictStrategy.UPSERT
            and conflict_target_columns
        ):
            update_cols = ", ".join(
                f'"{col}" = EXCLUDED."{col}"'
                for col in data.columns
                if col not in conflict_target_columns
            )
            if not update_cols:  # All columns are part of the conflict key
                # Fallback to DO NOTHING if there are no columns to update
                sql = f"""
                    INSERT INTO {import_config.table_name} ({columns})
                    VALUES %s
                    ON CONFLICT ({conflict_target_str}) DO NOTHING;
                """
            else:
                sql = f"""
                    INSERT INTO {import_config.table_name} ({columns})
                    VALUES %s
                    ON CONFLICT ({conflict_target_str}) DO UPDATE SET {update_cols};
                """
        else:  # 'ignore' or no conflict target available
            sql = f"""
                INSERT INTO {import_config.table_name} ({columns})
                VALUES %s
                ON CONFLICT DO NOTHING;
            """

        try:
            # 将 DataFrame 转换为记录列表
            records = data.to_records(index=False).tolist()

            # 使用 execute 方法执行带有 ON CONFLICT 的 SQL
            await self.connection.executemany(sql, records)

            logger.info(
                f"Loaded {len(data)} rows into '{import_config.table_name}' using '{import_config.conflict_strategy}' strategy."
            )
        except asyncpg.PostgresError as e:
            logger.error(f"Error loading data into PostgreSQL: {e}")
            raise


class PostgresMetadataProvider(PostgresConnector, MetadataProvider):
    """PostgreSQL 元数据查询提供者"""

    def __init__(self, config: PostgresConfig):
        """
        初始化 PostgreSQL 元数据查询提供者。

        参数:
            config: PostgreSQL 配置对象
        """
        super().__init__(config)

    def _map_postgres_type_to_column_type(self, pg_type: str) -> ColumnType:
        """将 PostgreSQL 数据类型映射到标准列类型"""
        pg_type = pg_type.lower()

        # 数值类型
        if pg_type in ("integer", "int", "int4"):
            return ColumnType.INTEGER
        elif pg_type in ("bigint", "int8"):
            return ColumnType.BIGINT
        elif pg_type in ("smallint", "int2"):
            return ColumnType.SMALLINT
        elif pg_type in ("decimal", "numeric"):
            return ColumnType.DECIMAL
        elif pg_type in ("real", "float4"):
            return ColumnType.FLOAT
        elif pg_type in ("double precision", "float8"):
            return ColumnType.DOUBLE

        # 字符串类型
        elif pg_type.startswith("character varying") or pg_type.startswith("varchar"):
            return ColumnType.VARCHAR
        elif pg_type.startswith("character") or pg_type.startswith("char"):
            return ColumnType.CHAR
        elif pg_type == "text":
            return ColumnType.TEXT

        # 日期时间类型
        elif pg_type == "date":
            return ColumnType.DATE
        elif pg_type.startswith("time"):
            return ColumnType.TIME
        elif pg_type.startswith("timestamp"):
            return ColumnType.TIMESTAMP

        # 二进制类型
        elif pg_type == "bytea":
            return ColumnType.BINARY

        # 布尔类型
        elif pg_type in ("boolean", "bool"):
            return ColumnType.BOOLEAN

        # JSON类型
        elif pg_type in ("json", "jsonb"):
            return ColumnType.JSON

        # UUID类型
        elif pg_type == "uuid":
            return ColumnType.UUID

        return ColumnType.UNKNOWN

    @override
    async def get_table_metadata(
        self, table_name: str, schema_name: str | None = None
    ) -> TableMetadata:
        """获取 PostgreSQL 表的元数据信息"""
        await self._connect()
        if not self.connection:
            raise RuntimeError("Failed to establish PostgreSQL connection")

        # 使用默认schema如果未指定
        schema = schema_name or self.config.schema_name or "public"

        # 检查表是否存在
        if not await self.table_exists(table_name, schema):
            raise ValueError(f"Table '{schema}.{table_name}' does not exist")

        # 查询列信息
        columns = await self._get_column_info(table_name, schema)

        # 查询索引信息
        indexes = await self._get_index_info(table_name, schema)

        # 查询约束信息
        constraints = await self._get_constraint_info(table_name, schema)

        # 查询表注释
        table_comment = await self._get_table_comment(table_name, schema)

        return TableMetadata(
            table_name=table_name,
            schema_name=schema,
            database_name=self.config.database,
            columns=columns,
            indexes=indexes,
            constraints=constraints,
            comment=table_comment,
        )

    async def _get_column_info(
        self, table_name: str, schema_name: str
    ) -> list[ColumnInfo]:
        """查询表的列信息"""
        if not self.connection:
            raise RuntimeError("No database connection")

        query = """
            SELECT
                c.column_name,
                c.data_type,
                c.is_nullable,
                c.column_default,
                c.character_maximum_length,
                c.numeric_precision,
                c.numeric_scale,
                CASE WHEN pk.column_name IS NOT NULL THEN true ELSE false END as is_primary_key,
                CASE WHEN uk.column_name IS NOT NULL THEN true ELSE false END as is_unique,
                col_description(pgc.oid, c.ordinal_position) as column_comment
            FROM information_schema.columns c
            LEFT JOIN (
                SELECT ku.column_name
                FROM information_schema.table_constraints tc
                JOIN information_schema.key_column_usage ku
                    ON tc.constraint_name = ku.constraint_name
                    AND tc.table_schema = ku.table_schema
                    AND tc.table_name = ku.table_name
                WHERE tc.constraint_type = 'PRIMARY KEY'
                AND tc.table_schema = $2 AND tc.table_name = $1
            ) pk ON c.column_name = pk.column_name
            LEFT JOIN (
                SELECT ku.column_name
                FROM information_schema.table_constraints tc
                JOIN information_schema.key_column_usage ku
                    ON tc.constraint_name = ku.constraint_name
                    AND tc.table_schema = ku.table_schema
                    AND tc.table_name = ku.table_name
                WHERE tc.constraint_type = 'UNIQUE'
                AND tc.table_schema = $2 AND tc.table_name = $1
            ) uk ON c.column_name = uk.column_name
            LEFT JOIN pg_class pgc ON pgc.relname = c.table_name
            LEFT JOIN pg_namespace pgn ON pgn.oid = pgc.relnamespace AND pgn.nspname = c.table_schema
            WHERE c.table_schema = $2 AND c.table_name = $1
            ORDER BY c.ordinal_position
        """

        rows = await self.connection.fetch(query, table_name, schema_name)

        columns = []
        for row in rows:
            is_auto_increment = row["column_default"] and "nextval" in str(
                row["column_default"]
            )

            columns.append(
                ColumnInfo(
                    name=row["column_name"],
                    data_type=self._map_postgres_type_to_column_type(row["data_type"]),
                    is_nullable=row["is_nullable"] == "YES",
                    is_primary_key=row["is_primary_key"],
                    is_unique=row["is_unique"] or row["is_primary_key"],
                    is_auto_increment=is_auto_increment,
                    default_value=row["column_default"],
                    max_length=row["character_maximum_length"],
                    precision=row["numeric_precision"],
                    scale=row["numeric_scale"],
                    comment=row["column_comment"],
                )
            )

        return columns

    async def _get_index_info(
        self, table_name: str, schema_name: str
    ) -> list[IndexInfo]:
        """查询表的索引信息"""
        if not self.connection:
            raise RuntimeError("No database connection")

        query = """
            SELECT
                i.indexname as index_name,
                i.indexdef,
                CASE WHEN c.contype = 'p' THEN true ELSE false END as is_primary,
                CASE WHEN c.contype = 'u' OR c.contype = 'p' THEN true ELSE false END as is_unique,
                obj_description(idx.oid) as comment
            FROM pg_indexes i
            LEFT JOIN pg_class t ON t.relname = i.tablename
            LEFT JOIN pg_namespace n ON n.nspname = i.schemaname AND n.oid = t.relnamespace
            LEFT JOIN pg_class idx ON idx.relname = i.indexname
            LEFT JOIN pg_constraint c ON c.conname = i.indexname AND c.conrelid = t.oid
            WHERE i.schemaname = $2 AND i.tablename = $1
            ORDER BY i.indexname
        """

        rows = await self.connection.fetch(query, table_name, schema_name)

        indexes = []
        for row in rows:
            # 从indexdef中解析列名
            indexdef = row["indexdef"]
            columns = self._parse_index_columns_from_def(indexdef)

            is_primary = row["is_primary"]
            is_unique = row["is_unique"]

            if is_primary:
                index_type = IndexType.PRIMARY
            elif is_unique:
                index_type = IndexType.UNIQUE
            else:
                index_type = IndexType.INDEX

            indexes.append(
                IndexInfo(
                    name=row["index_name"],
                    index_type=index_type,
                    columns=columns,
                    is_unique=is_unique,
                    is_primary=is_primary,
                    comment=row["comment"],
                )
            )

        return indexes

    def _parse_index_columns_from_def(self, indexdef: str) -> list[str]:
        """从索引定义中解析列名"""
        # 简单的解析逻辑，提取括号内的列名
        import re

        match = re.search(r"\((.*?)\)", indexdef)
        if match:
            columns_str = match.group(1)
            # 分割列名并清理空格和引号
            columns = [col.strip().strip('"') for col in columns_str.split(",")]
            return columns
        return []

    async def _get_constraint_info(
        self, table_name: str, schema_name: str
    ) -> list[ConstraintInfo]:
        """查询表的约束信息"""
        if not self.connection:
            raise RuntimeError("No database connection")

        query = """
            SELECT
                tc.constraint_name,
                tc.constraint_type,
                array_agg(kcu.column_name ORDER BY kcu.ordinal_position) as columns,
                ccu.table_name as referenced_table,
                array_agg(ccu.column_name ORDER BY kcu.ordinal_position) as referenced_columns,
                cc.check_clause
            FROM information_schema.table_constraints tc
            LEFT JOIN information_schema.key_column_usage kcu
                ON tc.constraint_name = kcu.constraint_name
                AND tc.table_schema = kcu.table_schema
                AND tc.table_name = kcu.table_name
            LEFT JOIN information_schema.constraint_column_usage ccu
                ON tc.constraint_name = ccu.constraint_name
                AND tc.table_schema = ccu.table_schema
            LEFT JOIN information_schema.check_constraints cc
                ON tc.constraint_name = cc.constraint_name
                AND tc.table_schema = cc.constraint_schema
            WHERE tc.table_schema = $2 AND tc.table_name = $1
            GROUP BY tc.constraint_name, tc.constraint_type, ccu.table_name, cc.check_clause
            ORDER BY tc.constraint_name
        """

        rows = await self.connection.fetch(query, table_name, schema_name)

        constraints = []
        for row in rows:
            constraint_type_str = row["constraint_type"]

            if constraint_type_str == "PRIMARY KEY":
                constraint_type = ConstraintType.PRIMARY_KEY
            elif constraint_type_str == "UNIQUE":
                constraint_type = ConstraintType.UNIQUE
            elif constraint_type_str == "FOREIGN KEY":
                constraint_type = ConstraintType.FOREIGN_KEY
            elif constraint_type_str == "CHECK":
                constraint_type = ConstraintType.CHECK
            else:
                continue  # 跳过不支持的约束类型

            constraints.append(
                ConstraintInfo(
                    name=row["constraint_name"],
                    constraint_type=constraint_type,
                    columns=list(row["columns"]) if row["columns"] else [],
                    referenced_table=row["referenced_table"],
                    referenced_columns=(
                        list(row["referenced_columns"])
                        if row["referenced_columns"]
                        else None
                    ),
                    check_clause=row["check_clause"],
                )
            )

        return constraints

    async def _get_table_comment(self, table_name: str, schema_name: str) -> str | None:
        """查询表注释"""
        if not self.connection:
            raise RuntimeError("No database connection")

        query = """
            SELECT obj_description(c.oid) as comment
            FROM pg_class c
            JOIN pg_namespace n ON n.oid = c.relnamespace
            WHERE n.nspname = $2 AND c.relname = $1
        """

        row = await self.connection.fetchrow(query, table_name, schema_name)
        return row["comment"] if row and row["comment"] else None

    @override
    async def table_exists(
        self, table_name: str, schema_name: str | None = None
    ) -> bool:
        """检查表是否存在"""
        await self._connect()
        if not self.connection:
            return False

        schema = schema_name or self.config.schema_name or "public"

        query = """
            SELECT COUNT(*)
            FROM information_schema.tables
            WHERE table_schema = $2 AND table_name = $1
        """

        row = await self.connection.fetchrow(query, table_name, schema)
        return row[0] > 0 if row else False

    @override
    async def get_database_name(self) -> str:
        """获取当前连接的数据库名称"""
        return self.config.database
