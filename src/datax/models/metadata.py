"""
元数据模型模块。

本模块定义了数据库表元数据的数据结构，包括列信息、索引信息、约束信息等。
用于支持动态查询目标表结构，替代静态的conflict_target配置。

主要数据结构：
- ColumnInfo: 列信息
- IndexInfo: 索引信息
- ConstraintInfo: 约束信息
- TableMetadata: 表元数据
"""

from enum import Enum
from typing import Any

from pydantic import BaseModel, Field


class ColumnType(str, Enum):
    """列数据类型枚举"""

    # 数值类型
    INTEGER = "integer"
    BIGINT = "bigint"
    SMALLINT = "smallint"
    TINYINT = "tinyint"
    DECIMAL = "decimal"
    NUMERIC = "numeric"
    FLOAT = "float"
    DOUBLE = "double"
    REAL = "real"

    # 字符串类型
    VARCHAR = "varchar"
    CHAR = "char"
    TEXT = "text"
    LONGTEXT = "longtext"
    MEDIUMTEXT = "mediumtext"

    # 日期时间类型
    DATE = "date"
    TIME = "time"
    DATETIME = "datetime"
    TIMESTAMP = "timestamp"
    YEAR = "year"

    # 二进制类型
    BINARY = "binary"
    VARBINARY = "varbinary"
    BLOB = "blob"
    LONGBLOB = "longblob"

    # 布尔类型
    BOOLEAN = "boolean"

    # JSON类型
    JSON = "json"

    # 其他类型
    UUID = "uuid"
    ENUM = "enum"
    SET = "set"
    UNKNOWN = "unknown"


class ConstraintType(str, Enum):
    """约束类型枚举"""

    PRIMARY_KEY = "primary_key"
    UNIQUE = "unique"
    FOREIGN_KEY = "foreign_key"
    CHECK = "check"
    NOT_NULL = "not_null"


class IndexType(str, Enum):
    """索引类型枚举"""

    PRIMARY = "primary"
    UNIQUE = "unique"
    INDEX = "index"
    FULLTEXT = "fulltext"
    SPATIAL = "spatial"


class ColumnInfo(BaseModel):
    """列信息数据结构"""

    name: str = Field(..., description="列名")
    data_type: ColumnType = Field(..., description="数据类型")
    is_nullable: bool = Field(default=True, description="是否允许NULL")
    is_primary_key: bool = Field(default=False, description="是否为主键")
    is_unique: bool = Field(default=False, description="是否有唯一约束")
    is_auto_increment: bool = Field(default=False, description="是否自增")
    default_value: Any | None = Field(default=None, description="默认值")
    max_length: int | None = Field(default=None, description="最大长度")
    precision: int | None = Field(default=None, description="数值精度")
    scale: int | None = Field(default=None, description="数值小数位数")
    comment: str | None = Field(default=None, description="列注释")


class IndexInfo(BaseModel):
    """索引信息数据结构"""

    name: str = Field(..., description="索引名称")
    index_type: IndexType = Field(..., description="索引类型")
    columns: list[str] = Field(..., description="索引包含的列名列表")
    is_unique: bool = Field(default=False, description="是否为唯一索引")
    is_primary: bool = Field(default=False, description="是否为主键索引")
    comment: str | None = Field(default=None, description="索引注释")


class ConstraintInfo(BaseModel):
    """约束信息数据结构"""

    name: str = Field(..., description="约束名称")
    constraint_type: ConstraintType = Field(..., description="约束类型")
    columns: list[str] = Field(..., description="约束涉及的列名列表")
    referenced_table: str | None = Field(default=None, description="外键引用的表名")
    referenced_columns: list[str] | None = Field(
        default=None, description="外键引用的列名列表"
    )
    check_clause: str | None = Field(default=None, description="CHECK约束的条件")


class TableMetadata(BaseModel):
    """表元数据信息"""

    table_name: str = Field(..., description="表名")
    schema_name: str | None = Field(default=None, description="模式名（PostgreSQL等）")
    database_name: str | None = Field(default=None, description="数据库名")
    columns: list[ColumnInfo] = Field(..., description="列信息列表")
    indexes: list[IndexInfo] = Field(default_factory=list, description="索引信息列表")
    constraints: list[ConstraintInfo] = Field(
        default_factory=list, description="约束信息列表"
    )
    comment: str | None = Field(default=None, description="表注释")

    def get_primary_key_columns(self) -> list[str]:
        """获取主键列名列表"""
        # 首先从列信息中查找主键
        pk_columns = [col.name for col in self.columns if col.is_primary_key]
        if pk_columns:
            return pk_columns

        # 如果列信息中没有主键标记，从索引中查找
        for index in self.indexes:
            if index.is_primary:
                return index.columns

        # 从约束中查找主键
        for constraint in self.constraints:
            if constraint.constraint_type == ConstraintType.PRIMARY_KEY:
                return constraint.columns

        return []

    def get_unique_key_columns(self) -> list[list[str]]:
        """获取所有唯一键列名列表（包括主键）"""
        unique_keys = []

        # 添加主键
        pk_columns = self.get_primary_key_columns()
        if pk_columns:
            unique_keys.append(pk_columns)

        # 从索引中查找唯一索引
        for index in self.indexes:
            if index.is_unique and not index.is_primary:
                unique_keys.append(index.columns)

        # 从约束中查找唯一约束
        for constraint in self.constraints:
            if constraint.constraint_type == ConstraintType.UNIQUE:
                unique_keys.append(constraint.columns)

        return unique_keys

    def get_column_by_name(self, column_name: str) -> ColumnInfo | None:
        """根据列名获取列信息"""
        for column in self.columns:
            if column.name == column_name:
                return column
        return None

    def filter_existing_columns(self, column_names: list[str]) -> list[str]:
        """过滤出在表中存在的列名"""
        existing_columns = {col.name for col in self.columns}
        return [col for col in column_names if col in existing_columns]
