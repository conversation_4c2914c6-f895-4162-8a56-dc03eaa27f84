"""
数据导入导出模块的配置类。

提供各种数据库连接配置、查询配置、转换器配置等。
"""

from .config import (
    AnyDatabaseConfig,
    ClickHouseConfig,
    ConflictStrategy,
    # 连接配置类
    ConnectionConfig,
    # 类型和策略定义
    DatasourceKind,
    # 作业配置
    ExportConfig,
    ImportConfig,
    MongoConfig,
    MySQLConfig,
    PostgresConfig,
    # 查询和转换配置
    QueryConfig,
    # 存储配置
    S3Config,
    TranslatorConfig,
)
from .metadata import (
    ColumnInfo,
    ColumnType,
    ConstraintInfo,
    ConstraintType,
    IndexInfo,
    IndexType,
    TableMetadata,
)

__all__ = [
    # 类型定义
    "DatasourceKind",
    "ConflictStrategy",
    # 连接配置
    "ConnectionConfig",
    "MySQLConfig",
    "PostgresConfig",
    "ClickHouseConfig",
    "MongoConfig",
    "AnyDatabaseConfig",
    # 查询和转换
    "QueryConfig",
    "TranslatorConfig",
    # 存储
    "S3Config",
    # 作业配置
    "ExportConfig",
    "ImportConfig",
    # 元数据模型
    "ColumnInfo",
    "ColumnType",
    "ConstraintInfo",
    "ConstraintType",
    "IndexInfo",
    "IndexType",
    "TableMetadata",
]
