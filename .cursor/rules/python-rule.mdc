---
description: 
globs: *.py
alwaysApply: false
---


# AI操作指令手册

## 技术栈

1. 使用python技术栈. 使用python3.12之后的python版本.
2. 不要直接用typing下的List,Dict,Set,Tuple,Optional等类型,而是使用内置的list,dict,set,tuple等类型.
3. 使用prefect v3作为执行引擎.
4. 使用uv管理venv环境.
5. 使用pytest进行测试,测试代码放到tests目录下.
6. 使用testcontainers启动容器.
7. 使用ruff作为lint和format的工具.

## 项目结构

```bash
.
├──.venv/                      # 由 uv 管理的虚拟环境
├── docs/                       # 项目文档 (例如,架构决策记录,API 文档)
├── scripts/                    # 辅助脚本 (例如,部署,数据初始化)
│   └── apply_s3_lifecycle.py   # 新增：用于应用S3生命周期策略的脚本
├── configs/                    # 新增：存放配置文件的目录
│   └── s3_lifecycle.yml        # 新增：S3生命周期策略配置文件
├── src/                        # 应用主源码目录
│   └── datax/          # Python 包
│       ├── __init__.py
│       ├── core/               # 核心抽象,如 Extractor, Translator, Loader
│       ├── connectors/         # 具体的数据库连接器实现
│       ├── flows/              # Prefect 工作流定义
│       ├── models/             # Pydantic 配置模型
│       └── utils/              # 通用工具函数
├── tests/                      # 测试代码
│   ├── __init__.py
│   ├── conftest.py             # Pytest 配置文件和全局 fixtures
│   ├── integration/            # 集成测试
│   └── unit/                   # 单元测试
├── pyproject.toml              # 项目配置的唯一真实来源
└── requirements.txt            # 由 uv 生成的锁定依赖文件
```

## 运行程序

1. 运行正式代码需要把src目录增加到PYTHONPATH中;
2. 通过pytest运行测试用例.
3. 通过ruff lint和format代码.

## 交流语言说明

1. 使用中文交流.
2. 函数doc和参数说明用中文.
3. 代码注释用中文.
4. 打印日志内容用英文.
5. git提交记录用英文.

## 使用异步客户端

1. 使用aioboto3作为s3客户端.
2. 使用ayncmy作为mysql客户端.
3. 使用ayncpg作为postgres客户端.
4. 使用motor作为mongodb客户端.
5. 使用aiosqlite作为sqlite客户端.
6. 使用aioch作为clickhouse客户端.
