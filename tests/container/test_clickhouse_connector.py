"""
ClickHouse 连接器测试
"""

import pandas as pd
import pytest

from src.datax.connectors.clickhouse import <PERSON><PERSON><PERSON><PERSON><PERSON>xtractor, ClickHouseLoader
from src.datax.models import QueryConfig


# 创建简化的测试配置类
class SimpleExportConfig:
    """简化的导出配置，仅用于测试"""

    def __init__(self, cursor_fetch_size=1000, parquet_chunk_size=1000):
        self.cursor_fetch_size = cursor_fetch_size
        self.parquet_chunk_size = parquet_chunk_size


class SimpleImportConfig:
    """简化的导入配置，仅用于测试"""

    def __init__(self, table_name, conflict_strategy="ignore"):
        self.table_name = table_name
        self.conflict_strategy = conflict_strategy


@pytest.mark.clickhouse
@pytest.mark.asyncio
async def test_clickhouse_extractor_basic_query(clickhouse_config):
    """测试 ClickHouse 提取器基本查询功能"""
    extractor = ClickHouseExtractor(clickhouse_config)

    query_config = QueryConfig(
        table="dummy",  # 当使用sql_query时，table字段仍然是必需的
        sql_query="SELECT 1 as test_col, 'hello' as text_col",
    )
    export_config = SimpleExportConfig(cursor_fetch_size=1000, parquet_chunk_size=1000)

    results = []
    async for chunk in extractor.extract_stream(query_config, export_config):
        results.append(chunk)

    assert len(results) == 1
    df = results[0]
    assert len(df) == 1
    assert df.iloc[0]["test_col"] == 1
    assert df.iloc[0]["text_col"] == "hello"


@pytest.mark.clickhouse
@pytest.mark.asyncio
async def test_clickhouse_loader_basic_insert(clickhouse_config, clickhouse_client):
    """测试 ClickHouse 加载器基本插入功能"""
    # 创建测试表
    await clickhouse_client.execute(
        """
        CREATE TABLE IF NOT EXISTS test_table (
            id UInt32,
            name String,
            value Float64
        ) ENGINE = Memory
    """
    )

    try:
        loader = ClickHouseLoader(clickhouse_config)

        # 准备测试数据
        test_data = pd.DataFrame(
            {
                "id": [1, 2, 3],
                "name": ["test1", "test2", "test3"],
                "value": [1.1, 2.2, 3.3],
            }
        )

        import_config = SimpleImportConfig(
            table_name="test_table", conflict_strategy="ignore"
        )

        # 插入数据
        await loader.load_chunk(test_data, import_config)

        # 验证数据
        result = await clickhouse_client.execute("SELECT COUNT(*) FROM test_table")
        assert result[0][0] == 3

        result = await clickhouse_client.execute("SELECT * FROM test_table ORDER BY id")
        assert len(result) == 3
        assert result[0] == (1, "test1", 1.1)
        assert result[1] == (2, "test2", 2.2)
        assert result[2] == (3, "test3", 3.3)

    finally:
        # 清理测试表
        await clickhouse_client.execute("DROP TABLE IF EXISTS test_table")


@pytest.mark.clickhouse
@pytest.mark.asyncio
async def test_clickhouse_extractor_table_query(clickhouse_config, clickhouse_client):
    """测试 ClickHouse 提取器表查询功能"""
    # 创建测试表并插入数据
    await clickhouse_client.execute(
        """
        CREATE TABLE IF NOT EXISTS test_extract_table (
            id UInt32,
            name String,
            created_at DateTime
        ) ENGINE = Memory
    """
    )

    await clickhouse_client.execute(
        """
        INSERT INTO test_extract_table VALUES
        (1, 'Alice', '2023-01-01 10:00:00'),
        (2, 'Bob', '2023-01-02 11:00:00'),
        (3, 'Charlie', '2023-01-03 12:00:00')
    """
    )

    try:
        extractor = ClickHouseExtractor(clickhouse_config)

        query_config = QueryConfig(
            table="test_extract_table", columns=["id", "name"], limit=2
        )
        export_config = SimpleExportConfig(
            cursor_fetch_size=1000, parquet_chunk_size=1000
        )

        results = []
        async for chunk in extractor.extract_stream(query_config, export_config):
            results.append(chunk)

        assert len(results) == 1
        df = results[0]
        assert len(df) == 2
        assert list(df.columns) == ["id", "name"]
        assert df.iloc[0]["id"] == 1
        assert df.iloc[0]["name"] == "Alice"
        assert df.iloc[1]["id"] == 2
        assert df.iloc[1]["name"] == "Bob"

    finally:
        # 清理测试表
        await clickhouse_client.execute("DROP TABLE IF EXISTS test_extract_table")


@pytest.mark.clickhouse
@pytest.mark.asyncio
async def test_clickhouse_connector_integration(clickhouse_config, clickhouse_client):
    """测试 ClickHouse 连接器的完整集成流程"""
    try:
        # 创建测试表
        await clickhouse_client.execute(
            """
            CREATE TABLE integration_test (
                id UInt32,
                name String,
                value Float64,
                created_at DateTime
            ) ENGINE = MergeTree()
            ORDER BY id
            """
        )

        # 使用 Loader 插入数据
        loader = ClickHouseLoader(clickhouse_config)
        test_data = pd.DataFrame(
            {
                "id": [1, 2, 3, 4, 5],
                "name": ["Alice", "Bob", "Charlie", "David", "Eve"],
                "value": [1.1, 2.2, 3.3, 4.4, 5.5],
                "created_at": pd.to_datetime(
                    [
                        "2023-01-01 10:00:00",
                        "2023-01-02 11:00:00",
                        "2023-01-03 12:00:00",
                        "2023-01-04 13:00:00",
                        "2023-01-05 14:00:00",
                    ]
                ),
            }
        )

        import_config = SimpleImportConfig(
            table_name="integration_test", conflict_strategy="ignore"
        )
        await loader.load_chunk(test_data, import_config)

        # 使用 Extractor 提取数据
        extractor = ClickHouseExtractor(clickhouse_config)
        query_config = QueryConfig(
            table="integration_test", columns=["id", "name", "value"], limit=3
        )
        export_config = SimpleExportConfig(
            cursor_fetch_size=1000, parquet_chunk_size=1000
        )

        results = []
        async for chunk in extractor.extract_stream(query_config, export_config):
            results.append(chunk)

        # 验证结果
        assert len(results) == 1
        df = results[0]
        assert len(df) == 3
        assert list(df.columns) == ["id", "name", "value"]
        assert df.iloc[0]["id"] == 1
        assert df.iloc[0]["name"] == "Alice"
        assert df.iloc[0]["value"] == 1.1

        # 验证数据库中的总记录数
        result = await clickhouse_client.execute(
            "SELECT COUNT(*) FROM integration_test"
        )
        assert result[0][0] == 5

    finally:
        # 清理测试表
        await clickhouse_client.execute("DROP TABLE IF EXISTS integration_test")
