"""
数据转换器模块的单元测试。

测试 Translator 抽象基类和 NoOpTranslator 实现。
"""

import pytest
import pandas as pd
from collections.abc import AsyncIterator
from unittest.mock import AsyncMock

from src.datax.core.translator import Translator, NoOpTranslator
from src.datax.models import TranslatorConfig


class MockTranslator(Translator):
    """用于测试的模拟转换器"""
    
    async def transform_stream(
        self,
        data_stream: AsyncIterator[pd.DataFrame],
        translator_config: TranslatorConfig | None = None,
    ) -> AsyncIterator[pd.DataFrame]:
        """模拟转换：为每个DataFrame添加一个新列"""
        async for chunk in data_stream:
            # 添加一个测试列
            chunk_copy = chunk.copy()
            chunk_copy['test_column'] = 'transformed'
            yield chunk_copy


class TestTranslator:
    """Translator 抽象基类测试"""
    
    def test_translator_is_abstract(self):
        """测试 Translator 是抽象类，不能直接实例化"""
        with pytest.raises(TypeError):
            Translator()
    
    @pytest.mark.asyncio
    async def test_mock_translator_transform_stream(self):
        """测试模拟转换器的转换功能"""
        # 准备测试数据
        test_data = [
            pd.DataFrame({'id': [1, 2], 'name': ['Alice', 'Bob']}),
            pd.DataFrame({'id': [3, 4], 'name': ['Charlie', 'David']})
        ]
        
        async def mock_data_stream():
            for chunk in test_data:
                yield chunk
        
        # 创建转换器并测试
        translator = MockTranslator()
        result_chunks = []
        
        async for transformed_chunk in translator.transform_stream(mock_data_stream()):
            result_chunks.append(transformed_chunk)
        
        # 验证结果
        assert len(result_chunks) == 2
        
        # 验证第一个块
        first_chunk = result_chunks[0]
        assert 'test_column' in first_chunk.columns
        assert all(first_chunk['test_column'] == 'transformed')
        assert first_chunk['id'].tolist() == [1, 2]
        assert first_chunk['name'].tolist() == ['Alice', 'Bob']
        
        # 验证第二个块
        second_chunk = result_chunks[1]
        assert 'test_column' in second_chunk.columns
        assert all(second_chunk['test_column'] == 'transformed')
        assert second_chunk['id'].tolist() == [3, 4]
        assert second_chunk['name'].tolist() == ['Charlie', 'David']


class TestNoOpTranslator:
    """NoOpTranslator 测试"""
    
    def test_noop_translator_instantiation(self):
        """测试 NoOpTranslator 可以正常实例化"""
        translator = NoOpTranslator()
        assert isinstance(translator, NoOpTranslator)
        assert isinstance(translator, Translator)
    
    @pytest.mark.asyncio
    async def test_noop_translator_passthrough(self):
        """测试 NoOpTranslator 直接传递数据不做修改"""
        # 准备测试数据
        test_data = [
            pd.DataFrame({'id': [1, 2], 'name': ['Alice', 'Bob']}),
            pd.DataFrame({'id': [3, 4], 'name': ['Charlie', 'David']}),
            pd.DataFrame({'id': [5], 'name': ['Eve']})
        ]
        
        async def mock_data_stream():
            for chunk in test_data:
                yield chunk
        
        # 创建 NoOpTranslator 并测试
        translator = NoOpTranslator()
        result_chunks = []
        
        async for chunk in translator.transform_stream(mock_data_stream()):
            result_chunks.append(chunk)
        
        # 验证结果：应该与输入完全相同
        assert len(result_chunks) == len(test_data)
        
        for i, (original, result) in enumerate(zip(test_data, result_chunks)):
            pd.testing.assert_frame_equal(original, result)
    
    @pytest.mark.asyncio
    async def test_noop_translator_with_config(self):
        """测试 NoOpTranslator 接受配置参数但不使用"""
        test_data = [pd.DataFrame({'id': [1], 'value': ['test']})]
        
        async def mock_data_stream():
            for chunk in test_data:
                yield chunk
        
        translator = NoOpTranslator()
        config = TranslatorConfig(kind="noop")
        
        result_chunks = []
        async for chunk in translator.transform_stream(mock_data_stream(), config):
            result_chunks.append(chunk)
        
        # 验证结果不受配置影响
        assert len(result_chunks) == 1
        pd.testing.assert_frame_equal(test_data[0], result_chunks[0])
    
    @pytest.mark.asyncio
    async def test_noop_translator_empty_stream(self):
        """测试 NoOpTranslator 处理空数据流"""
        async def empty_data_stream():
            # 空的异步生成器
            return
            yield  # 这行永远不会执行，但让类型检查器知道这是生成器
        
        translator = NoOpTranslator()
        result_chunks = []
        
        async for chunk in translator.transform_stream(empty_data_stream()):
            result_chunks.append(chunk)
        
        # 验证空流返回空结果
        assert len(result_chunks) == 0
    
    @pytest.mark.asyncio
    async def test_noop_translator_large_dataframe(self):
        """测试 NoOpTranslator 处理大型 DataFrame"""
        # 创建一个较大的测试 DataFrame
        large_df = pd.DataFrame({
            'id': range(10000),
            'value': [f'value_{i}' for i in range(10000)],
            'category': ['A', 'B', 'C'] * 3334  # 重复模式
        })
        
        async def large_data_stream():
            yield large_df
        
        translator = NoOpTranslator()
        result_chunks = []
        
        async for chunk in translator.transform_stream(large_data_stream()):
            result_chunks.append(chunk)
        
        # 验证大型 DataFrame 正确传递
        assert len(result_chunks) == 1
        pd.testing.assert_frame_equal(large_df, result_chunks[0])
        assert len(result_chunks[0]) == 10000
