"""
元数据模型的单元测试。

测试表元数据、列信息、约束和索引等模型类。
"""

import pytest
from pydantic import ValidationError

from src.datax.models.metadata import (
    ColumnType,
    ColumnInfo,
    ConstraintType,
    ConstraintInfo,
    IndexType,
    IndexInfo,
    TableMetadata
)


class TestColumnType:
    """ColumnType 枚举测试"""
    
    def test_column_type_values(self):
        """测试列类型枚举值"""
        assert ColumnType.INTEGER == "INTEGER"
        assert ColumnType.BIGINT == "BIGINT"
        assert ColumnType.SMALLINT == "SMALLINT"
        assert ColumnType.DECIMAL == "DECIMAL"
        assert ColumnType.FLOAT == "FLOAT"
        assert ColumnType.DOUBLE == "DOUBLE"
        assert ColumnType.VARCHAR == "VARCHAR"
        assert ColumnType.TEXT == "TEXT"
        assert ColumnType.CHAR == "CHAR"
        assert ColumnType.BOOLEAN == "BOOLEAN"
        assert ColumnType.DATE == "DATE"
        assert ColumnType.DATETIME == "DATETIME"
        assert ColumnType.TIMESTAMP == "TIMESTAMP"
        assert ColumnType.TIME == "TIME"
        assert ColumnType.JSON == "JSON"
        assert ColumnType.BLOB == "BLOB"
        assert ColumnType.UNKNOWN == "UNKNOWN"
    
    def test_column_type_membership(self):
        """测试列类型枚举成员"""
        expected_types = [
            "INTEGER", "BIGINT", "SMALLINT", "DECIMAL", "FLOAT", "DOUBLE",
            "VARCHAR", "TEXT", "CHAR", "BOOLEAN", "DATE", "DATETIME", 
            "TIMESTAMP", "TIME", "JSON", "BLOB", "UNKNOWN"
        ]
        
        actual_types = [ct.value for ct in ColumnType]
        for expected_type in expected_types:
            assert expected_type in actual_types


class TestConstraintType:
    """ConstraintType 枚举测试"""
    
    def test_constraint_type_values(self):
        """测试约束类型枚举值"""
        assert ConstraintType.PRIMARY_KEY == "PRIMARY_KEY"
        assert ConstraintType.FOREIGN_KEY == "FOREIGN_KEY"
        assert ConstraintType.UNIQUE == "UNIQUE"
        assert ConstraintType.CHECK == "CHECK"
        assert ConstraintType.NOT_NULL == "NOT_NULL"


class TestIndexType:
    """IndexType 枚举测试"""
    
    def test_index_type_values(self):
        """测试索引类型枚举值"""
        assert IndexType.BTREE == "BTREE"
        assert IndexType.HASH == "HASH"
        assert IndexType.FULLTEXT == "FULLTEXT"
        assert IndexType.SPATIAL == "SPATIAL"
        assert IndexType.UNIQUE == "UNIQUE"


class TestColumnInfo:
    """ColumnInfo 模型测试"""
    
    def test_column_info_basic(self):
        """测试基本列信息"""
        column = ColumnInfo(
            name="id",
            type=ColumnType.INTEGER,
            nullable=False,
            default=None,
            comment="Primary key column"
        )
        
        assert column.name == "id"
        assert column.type == ColumnType.INTEGER
        assert column.nullable is False
        assert column.default is None
        assert column.comment == "Primary key column"
    
    def test_column_info_with_default(self):
        """测试包含默认值的列信息"""
        column = ColumnInfo(
            name="status",
            type=ColumnType.VARCHAR,
            nullable=True,
            default="active",
            comment="User status"
        )
        
        assert column.name == "status"
        assert column.type == ColumnType.VARCHAR
        assert column.nullable is True
        assert column.default == "active"
        assert column.comment == "User status"
    
    def test_column_info_nullable_column(self):
        """测试可空列"""
        column = ColumnInfo(
            name="description",
            type=ColumnType.TEXT,
            nullable=True,
            default=None,
            comment=None
        )
        
        assert column.nullable is True
        assert column.default is None
        assert column.comment is None
    
    def test_column_info_validation_empty_name(self):
        """测试空列名验证"""
        with pytest.raises(ValidationError):
            ColumnInfo(
                name="",  # 空名称应该失败
                type=ColumnType.INTEGER,
                nullable=False,
                default=None,
                comment=None
            )
    
    def test_column_info_different_types(self):
        """测试不同数据类型的列"""
        test_cases = [
            (ColumnType.INTEGER, False, 0),
            (ColumnType.VARCHAR, True, "default_value"),
            (ColumnType.BOOLEAN, False, True),
            (ColumnType.DATETIME, True, None),
            (ColumnType.JSON, True, "{}"),
            (ColumnType.DECIMAL, False, "0.00")
        ]
        
        for col_type, nullable, default in test_cases:
            column = ColumnInfo(
                name=f"test_{col_type.value.lower()}",
                type=col_type,
                nullable=nullable,
                default=default,
                comment=f"Test {col_type.value} column"
            )
            
            assert column.type == col_type
            assert column.nullable == nullable
            assert column.default == default


class TestConstraintInfo:
    """ConstraintInfo 模型测试"""
    
    def test_constraint_info_primary_key(self):
        """测试主键约束"""
        constraint = ConstraintInfo(
            name="pk_users_id",
            type=ConstraintType.PRIMARY_KEY,
            columns=["id"],
            referenced_table=None,
            referenced_columns=None
        )
        
        assert constraint.name == "pk_users_id"
        assert constraint.type == ConstraintType.PRIMARY_KEY
        assert constraint.columns == ["id"]
        assert constraint.referenced_table is None
        assert constraint.referenced_columns is None
    
    def test_constraint_info_foreign_key(self):
        """测试外键约束"""
        constraint = ConstraintInfo(
            name="fk_orders_user_id",
            type=ConstraintType.FOREIGN_KEY,
            columns=["user_id"],
            referenced_table="users",
            referenced_columns=["id"]
        )
        
        assert constraint.name == "fk_orders_user_id"
        assert constraint.type == ConstraintType.FOREIGN_KEY
        assert constraint.columns == ["user_id"]
        assert constraint.referenced_table == "users"
        assert constraint.referenced_columns == ["id"]
    
    def test_constraint_info_unique(self):
        """测试唯一约束"""
        constraint = ConstraintInfo(
            name="uk_users_email",
            type=ConstraintType.UNIQUE,
            columns=["email"],
            referenced_table=None,
            referenced_columns=None
        )
        
        assert constraint.name == "uk_users_email"
        assert constraint.type == ConstraintType.UNIQUE
        assert constraint.columns == ["email"]
    
    def test_constraint_info_composite_unique(self):
        """测试复合唯一约束"""
        constraint = ConstraintInfo(
            name="uk_users_first_last_name",
            type=ConstraintType.UNIQUE,
            columns=["first_name", "last_name"],
            referenced_table=None,
            referenced_columns=None
        )
        
        assert constraint.columns == ["first_name", "last_name"]
        assert len(constraint.columns) == 2


class TestIndexInfo:
    """IndexInfo 模型测试"""
    
    def test_index_info_basic(self):
        """测试基本索引信息"""
        index = IndexInfo(
            name="idx_users_email",
            type=IndexType.BTREE,
            columns=["email"],
            unique=True
        )
        
        assert index.name == "idx_users_email"
        assert index.type == IndexType.BTREE
        assert index.columns == ["email"]
        assert index.unique is True
    
    def test_index_info_composite(self):
        """测试复合索引"""
        index = IndexInfo(
            name="idx_users_name_age",
            type=IndexType.BTREE,
            columns=["last_name", "first_name", "age"],
            unique=False
        )
        
        assert index.columns == ["last_name", "first_name", "age"]
        assert len(index.columns) == 3
        assert index.unique is False
    
    def test_index_info_different_types(self):
        """测试不同类型的索引"""
        test_cases = [
            (IndexType.BTREE, ["id"], True),
            (IndexType.HASH, ["status"], False),
            (IndexType.FULLTEXT, ["content"], False),
            (IndexType.UNIQUE, ["email"], True)
        ]
        
        for idx_type, columns, unique in test_cases:
            index = IndexInfo(
                name=f"idx_test_{idx_type.value.lower()}",
                type=idx_type,
                columns=columns,
                unique=unique
            )
            
            assert index.type == idx_type
            assert index.columns == columns
            assert index.unique == unique


class TestTableMetadata:
    """TableMetadata 模型测试"""
    
    def test_table_metadata_basic(self):
        """测试基本表元数据"""
        columns = [
            ColumnInfo(
                name="id",
                type=ColumnType.INTEGER,
                nullable=False,
                default=None,
                comment="Primary key"
            ),
            ColumnInfo(
                name="name",
                type=ColumnType.VARCHAR,
                nullable=False,
                default=None,
                comment="User name"
            )
        ]
        
        metadata = TableMetadata(
            table_name="users",
            schema_name="public",
            columns=columns,
            primary_keys=["id"],
            indexes=[],
            constraints=[]
        )
        
        assert metadata.table_name == "users"
        assert metadata.schema_name == "public"
        assert len(metadata.columns) == 2
        assert metadata.primary_keys == ["id"]
        assert metadata.indexes == []
        assert metadata.constraints == []
    
    def test_table_metadata_complete(self):
        """测试完整的表元数据"""
        columns = [
            ColumnInfo(
                name="id",
                type=ColumnType.INTEGER,
                nullable=False,
                default=None,
                comment="Primary key"
            ),
            ColumnInfo(
                name="email",
                type=ColumnType.VARCHAR,
                nullable=False,
                default=None,
                comment="User email"
            ),
            ColumnInfo(
                name="created_at",
                type=ColumnType.TIMESTAMP,
                nullable=False,
                default="CURRENT_TIMESTAMP",
                comment="Creation timestamp"
            )
        ]
        
        constraints = [
            ConstraintInfo(
                name="pk_users_id",
                type=ConstraintType.PRIMARY_KEY,
                columns=["id"],
                referenced_table=None,
                referenced_columns=None
            ),
            ConstraintInfo(
                name="uk_users_email",
                type=ConstraintType.UNIQUE,
                columns=["email"],
                referenced_table=None,
                referenced_columns=None
            )
        ]
        
        indexes = [
            IndexInfo(
                name="idx_users_email",
                type=IndexType.BTREE,
                columns=["email"],
                unique=True
            ),
            IndexInfo(
                name="idx_users_created_at",
                type=IndexType.BTREE,
                columns=["created_at"],
                unique=False
            )
        ]
        
        metadata = TableMetadata(
            table_name="users",
            schema_name="public",
            columns=columns,
            primary_keys=["id"],
            indexes=indexes,
            constraints=constraints
        )
        
        assert len(metadata.columns) == 3
        assert len(metadata.constraints) == 2
        assert len(metadata.indexes) == 2
        assert metadata.primary_keys == ["id"]
    
    def test_table_metadata_no_schema(self):
        """测试没有模式名的表元数据"""
        columns = [
            ColumnInfo(
                name="id",
                type=ColumnType.INTEGER,
                nullable=False,
                default=None,
                comment=None
            )
        ]
        
        metadata = TableMetadata(
            table_name="simple_table",
            schema_name=None,
            columns=columns,
            primary_keys=["id"],
            indexes=[],
            constraints=[]
        )
        
        assert metadata.schema_name is None
        assert metadata.table_name == "simple_table"
    
    def test_table_metadata_composite_primary_key(self):
        """测试复合主键的表元数据"""
        columns = [
            ColumnInfo(
                name="user_id",
                type=ColumnType.INTEGER,
                nullable=False,
                default=None,
                comment=None
            ),
            ColumnInfo(
                name="role_id",
                type=ColumnType.INTEGER,
                nullable=False,
                default=None,
                comment=None
            )
        ]
        
        metadata = TableMetadata(
            table_name="user_roles",
            schema_name=None,
            columns=columns,
            primary_keys=["user_id", "role_id"],
            indexes=[],
            constraints=[]
        )
        
        assert metadata.primary_keys == ["user_id", "role_id"]
        assert len(metadata.primary_keys) == 2
