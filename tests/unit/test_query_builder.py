"""
查询构建器模块的单元测试。

测试 SQLQueryBuilder 和 MongoQueryBuilder 的查询构建功能。
"""

import pytest
from unittest.mock import patch

from src.datax.connectors.query_builder import SQL<PERSON>ueryBuilder, MongoQueryBuilder


class TestSQLQueryBuilder:
    """SQLQueryBuilder 测试"""

    def test_build_select_query_basic(self):
        """测试基本的 SELECT 查询构建"""
        builder = SQLQueryBuilder()

        query = builder.build_select_query(
            table_name="users",
            columns=["id", "name", "email"],
            where_condition=None,
            limit=None,
        )

        expected = "SELECT id, name, email FROM users"
        assert query == expected

    def test_build_select_query_with_where(self):
        """测试带 WHERE 条件的 SELECT 查询"""
        builder = SQLQueryBuilder()

        query = builder.build_select_query(
            table_name="users",
            columns=["id", "name"],
            where_condition="active = 1",
            limit=None,
        )

        expected = "SELECT id, name FROM users WHERE active = 1"
        assert query == expected

    def test_build_select_query_with_limit(self):
        """测试带 LIMIT 的 SELECT 查询"""
        builder = SQLQueryBuilder()

        query = builder.build_select_query(
            table_name="users", columns=["*"], where_condition=None, limit=100
        )

        expected = "SELECT * FROM users LIMIT 100"
        assert query == expected

    def test_build_select_query_with_where_and_limit(self):
        """测试带 WHERE 和 LIMIT 的 SELECT 查询"""
        builder = SQLQueryBuilder()

        query = builder.build_select_query(
            table_name="orders",
            columns=["id", "user_id", "total"],
            where_condition="status = 'completed'",
            limit=50,
        )

        expected = (
            "SELECT id, user_id, total FROM orders WHERE status = 'completed' LIMIT 50"
        )
        assert query == expected

    def test_build_select_query_all_columns(self):
        """测试选择所有列的查询"""
        builder = SQLQueryBuilder()

        query = builder.build_select_query(
            table_name="products",
            columns=None,  # None 应该转换为 *
            where_condition=None,
            limit=None,
        )

        expected = "SELECT * FROM products"
        assert query == expected

    def test_build_select_query_empty_columns(self):
        """测试空列列表的查询"""
        builder = SQLQueryBuilder()

        query = builder.build_select_query(
            table_name="products",
            columns=[],  # 空列表应该转换为 *
            where_condition=None,
            limit=None,
        )

        expected = "SELECT * FROM products"
        assert query == expected

    def test_build_count_query_basic(self):
        """测试基本的 COUNT 查询"""
        builder = SQLQueryBuilder()

        query = builder.build_count_query(table_name="users", where_condition=None)

        expected = "SELECT COUNT(*) FROM users"
        assert query == expected

    def test_build_count_query_with_where(self):
        """测试带 WHERE 条件的 COUNT 查询"""
        builder = SQLQueryBuilder()

        query = builder.build_count_query(
            table_name="users", where_condition="active = 1 AND age > 18"
        )

        expected = "SELECT COUNT(*) FROM users WHERE active = 1 AND age > 18"
        assert query == expected

    def test_validate_sql_query_safe(self):
        """测试安全的 SQL 查询验证"""
        builder = SQLQueryBuilder()

        safe_queries = [
            "SELECT * FROM users",
            "SELECT id, name FROM products WHERE price > 100",
            "SELECT COUNT(*) FROM orders WHERE created_at > '2023-01-01'",
        ]

        for query in safe_queries:
            # 应该不抛出异常
            validated = builder._validate_sql_query(query)
            assert validated == query.strip()

    @patch("src.datax.connectors.query_builder.logger")
    def test_validate_sql_query_dangerous(self, mock_logger):
        """测试危险的 SQL 查询验证"""
        builder = SQLQueryBuilder()

        dangerous_queries = [
            "DROP TABLE users",
            "DELETE FROM users WHERE id = 1",
            "INSERT INTO users VALUES (1, 'test')",
            "UPDATE users SET name = 'hacked'",
            "SELECT * FROM users; DROP TABLE users;",
            "SELECT * FROM users -- comment",
        ]

        for query in dangerous_queries:
            # 应该记录警告但不抛出异常（在测试环境中）
            validated = builder._validate_sql_query(query)
            assert validated == query.strip()
            # 验证警告被记录
            assert mock_logger.warning.called

    def test_sanitize_condition_basic(self):
        """测试基本的条件清理"""
        builder = SQLQueryBuilder()

        conditions = [
            "id = 1",
            "name = 'John'",
            "age > 18 AND status = 'active'",
            "created_at BETWEEN '2023-01-01' AND '2023-12-31'",
        ]

        for condition in conditions:
            sanitized = builder._sanitize_condition(condition)
            assert sanitized == condition.strip()

    @patch("src.datax.connectors.query_builder.logger")
    def test_sanitize_condition_dangerous(self, mock_logger):
        """测试危险条件的清理"""
        builder = SQLQueryBuilder()

        dangerous_conditions = [
            "id = 1; DROP TABLE users",
            "name = 'test' OR 1=1 --",
            "id = 1 UNION SELECT * FROM passwords",
        ]

        for condition in dangerous_conditions:
            # 应该记录警告但不抛出异常（在测试环境中）
            sanitized = builder._sanitize_condition(condition)
            assert sanitized == condition.strip()
            # 验证警告被记录
            assert mock_logger.warning.called

    def test_build_insert_query(self):
        """测试 INSERT 查询构建"""
        builder = SQLQueryBuilder()

        query = builder.build_insert_query(
            table_name="users",
            columns=["name", "email", "age"],
            conflict_strategy="IGNORE",
        )

        expected = "INSERT IGNORE INTO users (name, email, age) VALUES (?, ?, ?)"
        assert query == expected

    def test_build_insert_query_replace(self):
        """测试 REPLACE INSERT 查询构建"""
        builder = SQLQueryBuilder()

        query = builder.build_insert_query(
            table_name="users",
            columns=["id", "name", "email"],
            conflict_strategy="REPLACE",
        )

        expected = "REPLACE INTO users (id, name, email) VALUES (?, ?, ?)"
        assert query == expected

    def test_build_insert_query_default_strategy(self):
        """测试默认冲突策略的 INSERT 查询"""
        builder = SQLQueryBuilder()

        query = builder.build_insert_query(
            table_name="products", columns=["name", "price"], conflict_strategy="UPSERT"
        )

        expected = "INSERT INTO products (name, price) VALUES (?, ?)"
        assert query == expected


class TestMongoQueryBuilder:
    """MongoQueryBuilder 测试"""

    def test_build_find_query_basic(self):
        """测试基本的 find 查询构建"""
        builder = MongoQueryBuilder()

        query = builder.build_find_query(
            collection_name="users", filter_dict=None, projection=None, limit=None
        )

        expected = {
            "collection": "users",
            "filter": {},
            "projection": None,
            "limit": None,
        }
        assert query == expected

    def test_build_find_query_with_filter(self):
        """测试带过滤条件的 find 查询"""
        builder = MongoQueryBuilder()

        filter_dict = {"status": "active", "age": {"$gte": 18}}
        query = builder.build_find_query(
            collection_name="users",
            filter_dict=filter_dict,
            projection=None,
            limit=None,
        )

        expected = {
            "collection": "users",
            "filter": {"status": "active", "age": {"$gte": 18}},
            "projection": None,
            "limit": None,
        }
        assert query == expected

    def test_build_find_query_with_projection(self):
        """测试带投影的 find 查询"""
        builder = MongoQueryBuilder()

        projection = {"name": 1, "email": 1, "_id": 0}
        query = builder.build_find_query(
            collection_name="users", filter_dict=None, projection=projection, limit=None
        )

        expected = {
            "collection": "users",
            "filter": {},
            "projection": {"name": 1, "email": 1, "_id": 0},
            "limit": None,
        }
        assert query == expected

    def test_build_find_query_with_limit(self):
        """测试带限制的 find 查询"""
        builder = MongoQueryBuilder()

        query = builder.build_find_query(
            collection_name="products", filter_dict=None, projection=None, limit=100
        )

        expected = {
            "collection": "products",
            "filter": {},
            "projection": None,
            "limit": 100,
        }
        assert query == expected

    def test_build_find_query_complete(self):
        """测试完整的 find 查询"""
        builder = MongoQueryBuilder()

        filter_dict = {"category": "electronics", "price": {"$lt": 1000}}
        projection = {"name": 1, "price": 1}

        query = builder.build_find_query(
            collection_name="products",
            filter_dict=filter_dict,
            projection=projection,
            limit=50,
        )

        expected = {
            "collection": "products",
            "filter": {"category": "electronics", "price": {"$lt": 1000}},
            "projection": {"name": 1, "price": 1},
            "limit": 50,
        }
        assert query == expected

    def test_build_count_query_basic(self):
        """测试基本的 count 查询"""
        builder = MongoQueryBuilder()

        query = builder.build_count_query(collection_name="users", filter_dict=None)

        expected = {"collection": "users", "filter": {}}
        assert query == expected

    def test_build_count_query_with_filter(self):
        """测试带过滤条件的 count 查询"""
        builder = MongoQueryBuilder()

        filter_dict = {"status": "active", "last_login": {"$gte": "2023-01-01"}}
        query = builder.build_count_query(
            collection_name="users", filter_dict=filter_dict
        )

        expected = {
            "collection": "users",
            "filter": {"status": "active", "last_login": {"$gte": "2023-01-01"}},
        }
        assert query == expected

    def test_build_insert_query(self):
        """测试 insert 查询构建"""
        builder = MongoQueryBuilder()

        document = {"name": "John", "email": "<EMAIL>", "age": 30}
        query = builder.build_insert_query(collection_name="users", document=document)

        expected = {
            "collection": "users",
            "document": {"name": "John", "email": "<EMAIL>", "age": 30},
        }
        assert query == expected

    def test_build_insert_many_query(self):
        """测试批量 insert 查询构建"""
        builder = MongoQueryBuilder()

        documents = [
            {"name": "John", "age": 30},
            {"name": "Jane", "age": 25},
            {"name": "Bob", "age": 35},
        ]

        query = builder.build_insert_many_query(
            collection_name="users", documents=documents
        )

        expected = {"collection": "users", "documents": documents}
        assert query == expected
