"""
配置模型的单元测试。

测试各种数据库配置类和相关的配置模型。
"""

import pytest
from pydantic import ValidationError, SecretStr

from src.datax.models.config import (
    DatasourceKind,
    ConflictStrategy,
    MySQLConfig,
    PostgresConfig,
    ClickHouseConfig,
    MongoConfig,
    S3Config,
    QueryConfig,
    TranslatorConfig,
    ExportConfig,
    ImportConfig,
)


class TestDatasourceKind:
    """DatasourceKind 枚举测试"""

    def test_datasource_kind_values(self):
        """测试数据源类型枚举值"""
        assert DatasourceKind.MYSQL == "mysql"
        assert DatasourceKind.POSTGRES == "postgres"
        assert DatasourceKind.CLICKHOUSE == "clickhouse"
        assert DatasourceKind.MONGODB == "mongodb"

    def test_datasource_kind_membership(self):
        """测试枚举成员检查"""
        valid_kinds = ["mysql", "postgres", "clickhouse", "mongodb"]
        for kind in valid_kinds:
            assert kind in [k.value for k in DatasourceKind]


class TestConflictStrategy:
    """ConflictStrategy 枚举测试"""

    def test_conflict_strategy_values(self):
        """测试冲突策略枚举值"""
        assert ConflictStrategy.IGNORE == "ignore"
        assert ConflictStrategy.REPLACE == "replace"
        assert ConflictStrategy.UPSERT == "upsert"


class TestMySQLConfig:
    """MySQLConfig 配置类测试"""

    def test_mysql_config_valid(self):
        """测试有效的 MySQL 配置"""
        config = MySQLConfig(
            kind=DatasourceKind.MYSQL,
            host="localhost",
            port=3306,
            database="test_db",
            username="test_user",
            password=SecretStr("test_password"),
        )

        assert config.kind == DatasourceKind.MYSQL
        assert config.host == "localhost"
        assert config.port == 3306
        assert config.database == "test_db"
        assert config.username == "test_user"
        assert config.password.get_secret_value() == "test_password"

    def test_mysql_config_with_optional_fields(self):
        """测试包含可选字段的 MySQL 配置"""
        config = MySQLConfig(
            kind=DatasourceKind.MYSQL,
            host="localhost",
            port=3306,
            database="test_db",
            username="test_user",
            password=SecretStr("test_password"),
            charset="utf8mb4",
            init_commands=["SET SESSION sql_mode='STRICT_TRANS_TABLES'"],
        )

        assert config.charset == "utf8mb4"
        assert config.init_commands == ["SET SESSION sql_mode='STRICT_TRANS_TABLES'"]

    def test_mysql_config_missing_required_fields(self):
        """测试缺少必需字段的 MySQL 配置"""
        with pytest.raises(ValidationError):
            MySQLConfig(
                kind=DatasourceKind.MYSQL,
                host="localhost",
                # 缺少 port, database, username, password
            )

    def test_mysql_config_invalid_port(self):
        """测试无效端口的 MySQL 配置"""
        with pytest.raises(ValidationError):
            MySQLConfig(
                kind=DatasourceKind.MYSQL,
                host="localhost",
                port=-1,  # 无效端口
                database="test_db",
                username="test_user",
                password=SecretStr("test_password"),
            )


class TestPostgresConfig:
    """PostgresConfig 配置类测试"""

    def test_postgres_config_valid(self):
        """测试有效的 PostgreSQL 配置"""
        config = PostgresConfig(
            kind=DatasourceKind.POSTGRES,
            host="localhost",
            port=5432,
            database="test_db",
            username="test_user",
            password=SecretStr("test_password"),
        )

        assert config.kind == DatasourceKind.POSTGRES
        assert config.host == "localhost"
        assert config.port == 5432
        assert config.database == "test_db"
        assert config.username == "test_user"
        assert config.password.get_secret_value() == "test_password"

    def test_postgres_config_with_schema(self):
        """测试包含模式的 PostgreSQL 配置"""
        config = PostgresConfig(
            kind=DatasourceKind.POSTGRES,
            host="localhost",
            port=5432,
            database="test_db",
            username="test_user",
            password=SecretStr("test_password"),
            schema="public",
        )

        assert config.schema == "public"


class TestClickHouseConfig:
    """ClickHouseConfig 配置类测试"""

    def test_clickhouse_config_valid(self):
        """测试有效的 ClickHouse 配置"""
        config = ClickHouseConfig(
            kind=DatasourceKind.CLICKHOUSE,
            host="localhost",
            port=8123,
            database="test_db",
            username="test_user",
            password=SecretStr("test_password"),
        )

        assert config.kind == DatasourceKind.CLICKHOUSE
        assert config.host == "localhost"
        assert config.port == 8123
        assert config.database == "test_db"
        assert config.username == "test_user"
        assert config.password.get_secret_value() == "test_password"


class TestMongoConfig:
    """MongoConfig 配置类测试"""

    def test_mongo_config_valid(self):
        """测试有效的 MongoDB 配置"""
        config = MongoConfig(
            kind=DatasourceKind.MONGODB,
            host="localhost",
            port=27017,
            database="test_db",
            username="test_user",
            password=SecretStr("test_password"),
        )

        assert config.kind == DatasourceKind.MONGODB
        assert config.host == "localhost"
        assert config.port == 27017
        assert config.database == "test_db"
        assert config.username == "test_user"
        assert config.password.get_secret_value() == "test_password"

    def test_mongo_config_with_collection(self):
        """测试包含集合名的 MongoDB 配置"""
        config = MongoConfig(
            kind=DatasourceKind.MONGODB,
            host="localhost",
            port=27017,
            database="test_db",
            username="test_user",
            password=SecretStr("test_password"),
            collection="test_collection",
        )

        assert config.collection == "test_collection"


class TestS3Config:
    """S3Config 配置类测试"""

    def test_s3_config_valid(self):
        """测试有效的 S3 配置"""
        config = S3Config(
            bucket="test-bucket",
            prefix="data/",
            access_key_id="test_access_key",
            secret_access_key=SecretStr("test_secret_key"),
            region="us-east-1",
        )

        assert config.bucket == "test-bucket"
        assert config.prefix == "data/"
        assert config.access_key_id == "test_access_key"
        assert config.secret_access_key.get_secret_value() == "test_secret_key"
        assert config.region == "us-east-1"

    def test_s3_config_with_bandwidth_limit(self):
        """测试包含带宽限制的 S3 配置"""
        config = S3Config(
            bucket="test-bucket",
            prefix="data/",
            access_key_id="test_access_key",
            secret_access_key=SecretStr("test_secret_key"),
            region="us-east-1",
            bandwidth_limit_mbps=100,
            max_concurrent_uploads=5,
        )

        assert config.bandwidth_limit_mbps == 100
        assert config.max_concurrent_uploads == 5


class TestQueryConfig:
    """QueryConfig 配置类测试"""

    def test_query_config_with_sql(self):
        """测试包含 SQL 查询的配置"""
        config = QueryConfig(
            table_name="users", sql_query="SELECT * FROM users WHERE active = 1"
        )

        assert config.sql_query == "SELECT * FROM users WHERE active = 1"
        assert config.table_name == "users"
        assert config.columns is None
        assert config.sql_condition is None

    def test_query_config_with_table_params(self):
        """测试包含表参数的配置"""
        config = QueryConfig(
            table_name="users",
            columns=["id", "name", "email"],
            sql_condition="active = 1",
        )

        assert config.table_name == "users"
        assert config.columns == ["id", "name", "email"]
        assert config.sql_condition == "active = 1"
        assert config.sql_query is None


class TestTranslatorConfig:
    """TranslatorConfig 配置类测试"""

    def test_translator_config_noop(self):
        """测试 noop 转换器配置"""
        config = TranslatorConfig(kind="noop")

        assert config.kind == "noop"


class TestExportConfig:
    """ExportConfig 配置类测试"""

    def test_export_config_valid(self):
        """测试有效的导出配置"""
        config = ExportConfig(
            cursor_fetch_size=1000, parquet_chunk_size=10000, rows_per_file=1000000
        )

        assert config.cursor_fetch_size == 1000
        assert config.parquet_chunk_size == 10000
        assert config.rows_per_file == 1000000

    def test_export_config_defaults(self):
        """测试导出配置的默认值"""
        config = ExportConfig()

        assert config.cursor_fetch_size == 1000
        assert config.parquet_chunk_size == 10000
        assert config.rows_per_file == 1000000


class TestImportConfig:
    """ImportConfig 配置类测试"""

    def test_import_config_valid(self):
        """测试有效的导入配置"""
        s3_config = S3Config(
            bucket="test-bucket",
            prefix="data/",
            access_key_id="test_access_key",
            secret_access_key=SecretStr("test_secret_key"),
            region="us-east-1",
        )

        config = ImportConfig(
            table_name="users",
            s3_source=s3_config,
            conflict_strategy=ConflictStrategy.REPLACE,
        )

        assert config.table_name == "users"
        assert config.s3_source is s3_config
        assert config.conflict_strategy == ConflictStrategy.REPLACE

    def test_import_config_with_translators(self):
        """测试包含转换器的导入配置"""
        s3_config = S3Config(
            bucket="test-bucket",
            prefix="data/",
            access_key_id="test_access_key",
            secret_access_key=SecretStr("test_secret_key"),
            region="us-east-1",
        )

        translator_config = TranslatorConfig(kind="noop")

        config = ImportConfig(
            table_name="users", s3_source=s3_config, translators=[translator_config]
        )

        assert config.translators is not None
        assert len(config.translators) == 1
        assert config.translators[0].kind == "noop"

    def test_import_config_default_conflict_strategy(self):
        """测试导入配置的默认冲突策略"""
        s3_config = S3Config(
            bucket="test-bucket",
            prefix="data/",
            access_key_id="test_access_key",
            secret_access_key=SecretStr("test_secret_key"),
            region="us-east-1",
        )

        config = ImportConfig(table_name="users", s3_source=s3_config)

        assert config.conflict_strategy == ConflictStrategy.IGNORE
