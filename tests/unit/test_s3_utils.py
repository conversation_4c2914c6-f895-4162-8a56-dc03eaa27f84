"""
S3 工具模块的单元测试。

测试 S3 相关的异步工具函数和 AsyncS3Client 类。
"""

import pytest
import tempfile
import os
from unittest.mock import AsyncMock, MagicMock, patch, mock_open
from botocore.exceptions import ClientError
from pydantic import SecretStr

from src.datax.utils.s3 import (
    upload_file_to_s3,
    download_file_from_s3,
    list_s3_objects,
    delete_s3_object,
    apply_s3_lifecycle_policy,
    upload_multiple_files_to_s3,
    AsyncS3Client
)
from src.datax.models import S3Config


@pytest.fixture
def s3_config():
    """创建测试用的 S3 配置"""
    return S3Config(
        bucket="test-bucket",
        access_key="test_access_key",
        secret_key=SecretStr("test_secret_key"),
        max_concurrency=5,
        max_bandwidth_mbps=10
    )


class TestUploadFileToS3:
    """upload_file_to_s3 函数测试"""
    
    @pytest.mark.asyncio
    @patch('src.datax.utils.s3.aioboto3.Session')
    async def test_upload_file_success(self, mock_session, s3_config):
        """测试成功上传文件"""
        # 模拟 S3 客户端
        mock_s3_client = AsyncMock()
        mock_session.return_value.client.return_value.__aenter__.return_value = mock_s3_client
        
        # 调用函数
        await upload_file_to_s3("local_file.txt", s3_config, "remote/file.txt")
        
        # 验证调用
        mock_s3_client.upload_file.assert_called_once_with(
            "local_file.txt",
            "test-bucket",
            "remote/file.txt",
            Config={
                "max_concurrency": 5,
                "max_bandwidth": 10 * 1024 * 1024,
            }
        )
    
    @pytest.mark.asyncio
    @patch('src.datax.utils.s3.aioboto3.Session')
    async def test_upload_file_client_error(self, mock_session, s3_config):
        """测试上传文件时的客户端错误"""
        # 模拟 S3 客户端抛出异常
        mock_s3_client = AsyncMock()
        mock_s3_client.upload_file.side_effect = ClientError(
            {"Error": {"Code": "NoSuchBucket", "Message": "Bucket does not exist"}},
            "upload_file"
        )
        mock_session.return_value.client.return_value.__aenter__.return_value = mock_s3_client
        
        # 验证异常被重新抛出
        with pytest.raises(ClientError):
            await upload_file_to_s3("local_file.txt", s3_config, "remote/file.txt")


class TestDownloadFileFromS3:
    """download_file_from_s3 函数测试"""
    
    @pytest.mark.asyncio
    @patch('src.datax.utils.s3.aioboto3.Session')
    async def test_download_file_success(self, mock_session, s3_config):
        """测试成功下载文件"""
        # 模拟 S3 客户端
        mock_s3_client = AsyncMock()
        mock_session.return_value.client.return_value.__aenter__.return_value = mock_s3_client
        
        # 调用函数
        await download_file_from_s3(s3_config, "remote/file.txt", "local_file.txt")
        
        # 验证调用
        mock_s3_client.download_file.assert_called_once_with(
            "test-bucket",
            "remote/file.txt",
            "local_file.txt",
            Config={
                "max_concurrency": 5,
                "max_bandwidth": 10 * 1024 * 1024,
            }
        )
    
    @pytest.mark.asyncio
    @patch('src.datax.utils.s3.aioboto3.Session')
    async def test_download_file_client_error(self, mock_session, s3_config):
        """测试下载文件时的客户端错误"""
        # 模拟 S3 客户端抛出异常
        mock_s3_client = AsyncMock()
        mock_s3_client.download_file.side_effect = ClientError(
            {"Error": {"Code": "NoSuchKey", "Message": "Key does not exist"}},
            "download_file"
        )
        mock_session.return_value.client.return_value.__aenter__.return_value = mock_s3_client
        
        # 验证异常被重新抛出
        with pytest.raises(ClientError):
            await download_file_from_s3(s3_config, "remote/file.txt", "local_file.txt")


class TestListS3Objects:
    """list_s3_objects 函数测试"""
    
    @pytest.mark.asyncio
    @patch('src.datax.utils.s3.aioboto3.Session')
    async def test_list_objects_success(self, mock_session, s3_config):
        """测试成功列出对象"""
        # 模拟 S3 客户端和分页器
        mock_s3_client = AsyncMock()
        mock_paginator = AsyncMock()
        
        # 模拟分页响应
        mock_pages = [
            {"Contents": [{"Key": "file1.txt"}, {"Key": "file2.txt"}]},
            {"Contents": [{"Key": "file3.txt"}]},
            {}  # 空页面
        ]
        
        async def mock_paginate(*args, **kwargs):
            for page in mock_pages:
                yield page
        
        mock_paginator.paginate.return_value = mock_paginate()
        mock_s3_client.get_paginator.return_value = mock_paginator
        mock_session.return_value.client.return_value.__aenter__.return_value = mock_s3_client
        
        # 调用函数并收集结果
        objects = []
        async for obj_key in list_s3_objects(s3_config, "prefix/"):
            objects.append(obj_key)
        
        # 验证结果
        assert objects == ["file1.txt", "file2.txt", "file3.txt"]
        mock_paginator.paginate.assert_called_once_with(Bucket="test-bucket", Prefix="prefix/")
    
    @pytest.mark.asyncio
    @patch('src.datax.utils.s3.aioboto3.Session')
    async def test_list_objects_empty_bucket(self, mock_session, s3_config):
        """测试列出空桶的对象"""
        # 模拟空的 S3 响应
        mock_s3_client = AsyncMock()
        mock_paginator = AsyncMock()
        
        async def mock_paginate(*args, **kwargs):
            yield {}  # 空响应
        
        mock_paginator.paginate.return_value = mock_paginate()
        mock_s3_client.get_paginator.return_value = mock_paginator
        mock_session.return_value.client.return_value.__aenter__.return_value = mock_s3_client
        
        # 调用函数并收集结果
        objects = []
        async for obj_key in list_s3_objects(s3_config):
            objects.append(obj_key)
        
        # 验证结果为空
        assert objects == []


class TestDeleteS3Object:
    """delete_s3_object 函数测试"""
    
    @pytest.mark.asyncio
    @patch('src.datax.utils.s3.aioboto3.Session')
    async def test_delete_object_success(self, mock_session, s3_config):
        """测试成功删除对象"""
        # 模拟 S3 客户端
        mock_s3_client = AsyncMock()
        mock_session.return_value.client.return_value.__aenter__.return_value = mock_s3_client
        
        # 调用函数
        await delete_s3_object(s3_config, "file_to_delete.txt")
        
        # 验证调用
        mock_s3_client.delete_object.assert_called_once_with(
            Bucket="test-bucket",
            Key="file_to_delete.txt"
        )
    
    @pytest.mark.asyncio
    @patch('src.datax.utils.s3.aioboto3.Session')
    async def test_delete_object_client_error(self, mock_session, s3_config):
        """测试删除对象时的客户端错误"""
        # 模拟 S3 客户端抛出异常
        mock_s3_client = AsyncMock()
        mock_s3_client.delete_object.side_effect = ClientError(
            {"Error": {"Code": "NoSuchKey", "Message": "Key does not exist"}},
            "delete_object"
        )
        mock_session.return_value.client.return_value.__aenter__.return_value = mock_s3_client
        
        # 验证异常被重新抛出
        with pytest.raises(ClientError):
            await delete_s3_object(s3_config, "nonexistent_file.txt")


class TestApplyS3LifecyclePolicy:
    """apply_s3_lifecycle_policy 函数测试"""
    
    @pytest.mark.asyncio
    @patch('src.datax.utils.s3.aioboto3.Session')
    @patch('builtins.open', new_callable=mock_open)
    @patch('src.datax.utils.s3.yaml.safe_load')
    async def test_apply_lifecycle_policy_success(self, mock_yaml_load, mock_file, mock_session):
        """测试成功应用生命周期策略"""
        # 模拟 YAML 配置
        mock_config = {
            "rules": [
                {
                    "id": "delete-old-files",
                    "prefix": "logs/",
                    "expiration_days": 30
                }
            ]
        }
        mock_yaml_load.return_value = mock_config
        
        # 模拟 S3 客户端
        mock_s3_client = AsyncMock()
        mock_session.return_value.client.return_value.__aenter__.return_value = mock_s3_client
        
        # 调用函数
        await apply_s3_lifecycle_policy("test-bucket", "config.yml")
        
        # 验证文件读取
        mock_file.assert_called_once_with("config.yml", "rb")
        mock_yaml_load.assert_called_once()
        
        # 验证生命周期策略应用
        expected_rules = [
            {
                "ID": "delete-old-files",
                "Filter": {"Prefix": "logs/"},
                "Status": "Enabled",
                "Expiration": {"Days": 30}
            }
        ]
        mock_s3_client.put_bucket_lifecycle_configuration.assert_called_once_with(
            Bucket="test-bucket",
            LifecycleConfiguration={"Rules": expected_rules}
        )
    
    @pytest.mark.asyncio
    @patch('builtins.open', side_effect=FileNotFoundError)
    async def test_apply_lifecycle_policy_file_not_found(self, mock_file):
        """测试配置文件不存在的情况"""
        with pytest.raises(SystemExit):
            await apply_s3_lifecycle_policy("test-bucket", "nonexistent.yml")


class TestUploadMultipleFilesToS3:
    """upload_multiple_files_to_s3 函数测试"""
    
    @pytest.mark.asyncio
    @patch('src.datax.utils.s3.upload_file_to_s3')
    async def test_upload_multiple_files_success(self, mock_upload_file, s3_config):
        """测试成功批量上传文件"""
        # 准备文件映射
        file_mappings = [
            ("local1.txt", "remote1.txt"),
            ("local2.txt", "remote2.txt"),
            ("local3.txt", "remote3.txt")
        ]
        
        # 调用函数
        await upload_multiple_files_to_s3(file_mappings, s3_config, max_concurrent_uploads=2)
        
        # 验证每个文件都被上传
        assert mock_upload_file.call_count == 3
        expected_calls = [
            (("local1.txt", s3_config, "remote1.txt"), {}),
            (("local2.txt", s3_config, "remote2.txt"), {}),
            (("local3.txt", s3_config, "remote3.txt"), {})
        ]
        
        actual_calls = [(call.args, call.kwargs) for call in mock_upload_file.call_args_list]
        for expected_call in expected_calls:
            assert expected_call in actual_calls
    
    @pytest.mark.asyncio
    @patch('src.datax.utils.s3.upload_file_to_s3')
    async def test_upload_multiple_files_empty_list(self, mock_upload_file, s3_config):
        """测试空文件列表的批量上传"""
        await upload_multiple_files_to_s3([], s3_config)
        
        # 验证没有文件被上传
        mock_upload_file.assert_not_called()


class TestAsyncS3Client:
    """AsyncS3Client 类测试"""
    
    def test_client_initialization(self, s3_config):
        """测试客户端初始化"""
        client = AsyncS3Client(s3_config)
        
        assert client.s3_config is s3_config
        assert client.client is None
        assert client.session is not None
    
    @pytest.mark.asyncio
    async def test_client_not_initialized_error(self, s3_config):
        """测试客户端未初始化时的错误"""
        client = AsyncS3Client(s3_config)
        
        # 测试各种方法在未初始化时抛出错误
        with pytest.raises(RuntimeError, match="Client not initialized"):
            await client.upload_file("local.txt", "remote.txt")
        
        with pytest.raises(RuntimeError, match="Client not initialized"):
            await client.download_file("remote.txt", "local.txt")
        
        with pytest.raises(RuntimeError, match="Client not initialized"):
            await client.delete_object("remote.txt")
        
        with pytest.raises(RuntimeError, match="Client not initialized"):
            async for _ in client.list_objects():
                pass
    
    @pytest.mark.asyncio
    @patch('src.datax.utils.s3.aioboto3.Session')
    async def test_client_context_manager(self, mock_session, s3_config):
        """测试客户端上下文管理器"""
        mock_s3_client = AsyncMock()
        mock_session.return_value.client.return_value = mock_s3_client
        
        client = AsyncS3Client(s3_config)
        
        # 测试进入上下文
        async with client as ctx_client:
            assert ctx_client is client
            assert client.client is not None
        
        # 验证客户端的 __aenter__ 和 __aexit__ 被调用
        mock_s3_client.__aenter__.assert_called_once()
        mock_s3_client.__aexit__.assert_called_once()
    
    @pytest.mark.asyncio
    @patch('src.datax.utils.s3.aioboto3.Session')
    async def test_client_upload_file(self, mock_session, s3_config):
        """测试客户端上传文件"""
        mock_s3_client = AsyncMock()
        mock_session.return_value.client.return_value = mock_s3_client
        
        async with AsyncS3Client(s3_config) as client:
            await client.upload_file("local.txt", "remote.txt")
            
            mock_s3_client.upload_file.assert_called_once_with(
                "local.txt",
                "test-bucket",
                "remote.txt",
                Config={
                    "max_concurrency": 5,
                    "max_bandwidth": 10 * 1024 * 1024,
                }
            )
