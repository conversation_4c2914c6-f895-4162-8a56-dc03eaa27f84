"""
元数据模块的单元测试。

测试 MetadataProvider、MetadataCache 和 CachedMetadataProvider 的功能。
"""

import pytest
from unittest.mock import AsyncMock, MagicMock

from src.datax.core.metadata import MetadataProvider, MetadataCache, CachedMetadataProvider
from src.datax.models.metadata import TableMetadata, ColumnInfo, ColumnType


class MockMetadataProvider(MetadataProvider):
    """用于测试的模拟元数据提供者"""
    
    def __init__(self):
        self.get_table_metadata_calls = []
        self.table_exists_calls = []
        self.database_name = "test_db"
    
    async def get_table_metadata(
        self, table_name: str, schema_name: str | None = None
    ) -> TableMetadata:
        """模拟获取表元数据"""
        self.get_table_metadata_calls.append((table_name, schema_name))
        
        # 返回模拟的表元数据
        columns = [
            ColumnInfo(
                name="id",
                type=ColumnType.INTEGER,
                nullable=False,
                default=None,
                comment="Primary key"
            ),
            ColumnInfo(
                name="name",
                type=ColumnType.VARCHAR,
                nullable=True,
                default=None,
                comment="User name"
            )
        ]
        
        return TableMetadata(
            table_name=table_name,
            schema_name=schema_name,
            columns=columns,
            primary_keys=["id"],
            indexes=[],
            constraints=[]
        )
    
    async def table_exists(
        self, table_name: str, schema_name: str | None = None
    ) -> bool:
        """模拟检查表是否存在"""
        self.table_exists_calls.append((table_name, schema_name))
        return table_name != "nonexistent_table"
    
    async def get_database_name(self) -> str:
        """返回模拟的数据库名"""
        return self.database_name


class TestMetadataProvider:
    """MetadataProvider 抽象基类测试"""
    
    def test_metadata_provider_is_abstract(self):
        """测试 MetadataProvider 是抽象类，不能直接实例化"""
        with pytest.raises(TypeError):
            MetadataProvider()
    
    @pytest.mark.asyncio
    async def test_mock_metadata_provider(self):
        """测试模拟元数据提供者的基本功能"""
        provider = MockMetadataProvider()
        
        # 测试获取表元数据
        metadata = await provider.get_table_metadata("users", "public")
        assert metadata.table_name == "users"
        assert metadata.schema_name == "public"
        assert len(metadata.columns) == 2
        assert metadata.columns[0].name == "id"
        assert metadata.columns[1].name == "name"
        assert metadata.primary_keys == ["id"]
        
        # 测试表存在检查
        exists = await provider.table_exists("users")
        assert exists is True
        
        not_exists = await provider.table_exists("nonexistent_table")
        assert not_exists is False
        
        # 测试数据库名获取
        db_name = await provider.get_database_name()
        assert db_name == "test_db"
        
        # 验证调用记录
        assert len(provider.get_table_metadata_calls) == 1
        assert provider.get_table_metadata_calls[0] == ("users", "public")
        assert len(provider.table_exists_calls) == 2


class TestMetadataCache:
    """MetadataCache 测试"""
    
    def test_cache_initialization(self):
        """测试缓存初始化"""
        cache = MetadataCache(max_size=50)
        assert cache.size() == 0
        assert cache._max_size == 50
    
    def test_cache_key_generation(self):
        """测试缓存键生成"""
        cache = MetadataCache()
        
        # 测试不同的键生成场景
        key1 = cache._make_cache_key("users")
        assert key1 == "users"
        
        key2 = cache._make_cache_key("users", "public")
        assert key2 == "public.users"
        
        key3 = cache._make_cache_key("users", "public", "test_db")
        assert key3 == "test_db.public.users"
        
        key4 = cache._make_cache_key("users", None, "test_db")
        assert key4 == "test_db.users"
    
    def test_cache_put_and_get(self):
        """测试缓存存储和获取"""
        cache = MetadataCache()
        
        # 创建测试元数据
        metadata = TableMetadata(
            table_name="users",
            schema_name="public",
            columns=[],
            primary_keys=[],
            indexes=[],
            constraints=[]
        )
        
        # 测试缓存未命中
        result = cache.get("users", "public", "test_db")
        assert result is None
        
        # 存储到缓存
        cache.put(metadata, "public", "test_db")
        assert cache.size() == 1
        
        # 测试缓存命中
        result = cache.get("users", "public", "test_db")
        assert result is not None
        assert result.table_name == "users"
        assert result.schema_name == "public"
    
    def test_cache_lru_eviction(self):
        """测试 LRU 缓存淘汰机制"""
        cache = MetadataCache(max_size=2)
        
        # 创建测试元数据
        metadata1 = TableMetadata(
            table_name="table1", schema_name=None, columns=[], 
            primary_keys=[], indexes=[], constraints=[]
        )
        metadata2 = TableMetadata(
            table_name="table2", schema_name=None, columns=[], 
            primary_keys=[], indexes=[], constraints=[]
        )
        metadata3 = TableMetadata(
            table_name="table3", schema_name=None, columns=[], 
            primary_keys=[], indexes=[], constraints=[]
        )
        
        # 添加两个条目，填满缓存
        cache.put(metadata1)
        cache.put(metadata2)
        assert cache.size() == 2
        
        # 访问第一个条目，使其成为最近使用的
        result = cache.get("table1")
        assert result is not None
        
        # 添加第三个条目，应该淘汰 table2
        cache.put(metadata3)
        assert cache.size() == 2
        
        # table1 应该还在缓存中
        assert cache.get("table1") is not None
        # table2 应该被淘汰
        assert cache.get("table2") is None
        # table3 应该在缓存中
        assert cache.get("table3") is not None
    
    def test_cache_remove(self):
        """测试缓存移除功能"""
        cache = MetadataCache()
        
        metadata = TableMetadata(
            table_name="users", schema_name=None, columns=[], 
            primary_keys=[], indexes=[], constraints=[]
        )
        
        # 添加到缓存
        cache.put(metadata)
        assert cache.size() == 1
        assert cache.get("users") is not None
        
        # 移除存在的条目
        removed = cache.remove("users")
        assert removed is True
        assert cache.size() == 0
        assert cache.get("users") is None
        
        # 尝试移除不存在的条目
        removed = cache.remove("nonexistent")
        assert removed is False
    
    def test_cache_clear(self):
        """测试缓存清空功能"""
        cache = MetadataCache()
        
        # 添加多个条目
        for i in range(5):
            metadata = TableMetadata(
                table_name=f"table{i}", schema_name=None, columns=[], 
                primary_keys=[], indexes=[], constraints=[]
            )
            cache.put(metadata)
        
        assert cache.size() == 5
        
        # 清空缓存
        cache.clear()
        assert cache.size() == 0
        
        # 验证所有条目都被清除
        for i in range(5):
            assert cache.get(f"table{i}") is None


class TestCachedMetadataProvider:
    """CachedMetadataProvider 测试"""
    
    @pytest.mark.asyncio
    async def test_cached_provider_initialization(self):
        """测试带缓存的元数据提供者初始化"""
        mock_provider = MockMetadataProvider()
        cached_provider = CachedMetadataProvider(mock_provider, cache_size=10)
        
        assert cached_provider.provider is mock_provider
        assert cached_provider.cache._max_size == 10
    
    @pytest.mark.asyncio
    async def test_cached_provider_cache_miss_and_hit(self):
        """测试缓存未命中和命中的情况"""
        mock_provider = MockMetadataProvider()
        cached_provider = CachedMetadataProvider(mock_provider)
        
        # 第一次调用 - 缓存未命中
        metadata1 = await cached_provider.get_table_metadata("users", "public")
        assert metadata1.table_name == "users"
        assert len(mock_provider.get_table_metadata_calls) == 1
        
        # 第二次调用相同表 - 缓存命中
        metadata2 = await cached_provider.get_table_metadata("users", "public")
        assert metadata2.table_name == "users"
        # 底层提供者不应该被再次调用
        assert len(mock_provider.get_table_metadata_calls) == 1
        
        # 调用不同表 - 缓存未命中
        metadata3 = await cached_provider.get_table_metadata("orders", "public")
        assert metadata3.table_name == "orders"
        assert len(mock_provider.get_table_metadata_calls) == 2
    
    @pytest.mark.asyncio
    async def test_cached_provider_table_exists(self):
        """测试表存在检查（不使用缓存）"""
        mock_provider = MockMetadataProvider()
        cached_provider = CachedMetadataProvider(mock_provider)
        
        # table_exists 应该直接调用底层提供者
        exists = await cached_provider.table_exists("users", "public")
        assert exists is True
        assert len(mock_provider.table_exists_calls) == 1
        
        # 再次调用应该再次调用底层提供者（不缓存）
        exists = await cached_provider.table_exists("users", "public")
        assert exists is True
        assert len(mock_provider.table_exists_calls) == 2
