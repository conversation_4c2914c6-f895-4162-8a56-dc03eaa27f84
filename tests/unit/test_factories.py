"""
工厂方法模块的单元测试。

测试 get_extractor、get_loader 和 get_translator 工厂函数。
"""

import pytest
from unittest.mock import patch, MagicMock

from src.datax.core.factories import get_extractor, get_loader, get_translator
from src.datax.core.translator import NoOpTranslator
from src.datax.models import (
    DatasourceKind,
    MySQLConfig,
    PostgresConfig,
    ClickHouseConfig,
    MongoConfig,
    TranslatorConfig
)
from pydantic import SecretStr


class TestGetExtractor:
    """get_extractor 工厂函数测试"""
    
    @patch('src.datax.core.factories.PostgresExtractor')
    def test_get_postgres_extractor(self, mock_postgres_extractor):
        """测试创建 PostgreSQL 提取器"""
        # 创建 PostgreSQL 配置
        config = PostgresConfig(
            kind=DatasourceKind.POSTGRES,
            host="localhost",
            port=5432,
            database="test_db",
            username="test_user",
            password=SecretStr("test_password")
        )
        
        # 模拟提取器实例
        mock_instance = MagicMock()
        mock_postgres_extractor.return_value = mock_instance
        
        # 调用工厂函数
        result = get_extractor(config)
        
        # 验证结果
        assert result is mock_instance
        mock_postgres_extractor.assert_called_once_with(config)
    
    @patch('src.datax.core.factories.MySQLExtractor')
    def test_get_mysql_extractor(self, mock_mysql_extractor):
        """测试创建 MySQL 提取器"""
        config = MySQLConfig(
            kind=DatasourceKind.MYSQL,
            host="localhost",
            port=3306,
            database="test_db",
            username="test_user",
            password=SecretStr("test_password")
        )
        
        mock_instance = MagicMock()
        mock_mysql_extractor.return_value = mock_instance
        
        result = get_extractor(config)
        
        assert result is mock_instance
        mock_mysql_extractor.assert_called_once_with(config)
    
    @patch('src.datax.core.factories.ClickHouseExtractor')
    def test_get_clickhouse_extractor(self, mock_clickhouse_extractor):
        """测试创建 ClickHouse 提取器"""
        config = ClickHouseConfig(
            kind=DatasourceKind.CLICKHOUSE,
            host="localhost",
            port=8123,
            database="test_db",
            username="test_user",
            password=SecretStr("test_password")
        )
        
        mock_instance = MagicMock()
        mock_clickhouse_extractor.return_value = mock_instance
        
        result = get_extractor(config)
        
        assert result is mock_instance
        mock_clickhouse_extractor.assert_called_once_with(config)
    
    @patch('src.datax.core.factories.MongoExtractor')
    def test_get_mongo_extractor(self, mock_mongo_extractor):
        """测试创建 MongoDB 提取器"""
        config = MongoConfig(
            kind=DatasourceKind.MONGODB,
            host="localhost",
            port=27017,
            database="test_db",
            username="test_user",
            password=SecretStr("test_password")
        )
        
        mock_instance = MagicMock()
        mock_mongo_extractor.return_value = mock_instance
        
        result = get_extractor(config)
        
        assert result is mock_instance
        mock_mongo_extractor.assert_called_once_with(config)
    
    def test_get_extractor_unsupported_kind(self):
        """测试不支持的数据源类型"""
        # 创建一个无效的配置（通过修改 kind 属性）
        config = MySQLConfig(
            kind=DatasourceKind.MYSQL,
            host="localhost",
            port=3306,
            database="test_db",
            username="test_user",
            password=SecretStr("test_password")
        )
        # 手动设置为不支持的类型
        config.kind = "unsupported_type"  # type: ignore
        
        with pytest.raises(ValueError) as exc_info:
            get_extractor(config)
        
        assert "Unsupported database kind for extractor" in str(exc_info.value)


class TestGetLoader:
    """get_loader 工厂函数测试"""
    
    @patch('src.datax.core.factories.PostgresLoader')
    def test_get_postgres_loader(self, mock_postgres_loader):
        """测试创建 PostgreSQL 加载器"""
        config = PostgresConfig(
            kind=DatasourceKind.POSTGRES,
            host="localhost",
            port=5432,
            database="test_db",
            username="test_user",
            password=SecretStr("test_password")
        )
        
        mock_instance = MagicMock()
        mock_postgres_loader.return_value = mock_instance
        
        result = get_loader(config)
        
        assert result is mock_instance
        mock_postgres_loader.assert_called_once_with(config)
    
    @patch('src.datax.core.factories.MySQLLoader')
    def test_get_mysql_loader(self, mock_mysql_loader):
        """测试创建 MySQL 加载器"""
        config = MySQLConfig(
            kind=DatasourceKind.MYSQL,
            host="localhost",
            port=3306,
            database="test_db",
            username="test_user",
            password=SecretStr("test_password")
        )
        
        mock_instance = MagicMock()
        mock_mysql_loader.return_value = mock_instance
        
        result = get_loader(config)
        
        assert result is mock_instance
        mock_mysql_loader.assert_called_once_with(config)
    
    @patch('src.datax.core.factories.ClickHouseLoader')
    def test_get_clickhouse_loader(self, mock_clickhouse_loader):
        """测试创建 ClickHouse 加载器"""
        config = ClickHouseConfig(
            kind=DatasourceKind.CLICKHOUSE,
            host="localhost",
            port=8123,
            database="test_db",
            username="test_user",
            password=SecretStr("test_password")
        )
        
        mock_instance = MagicMock()
        mock_clickhouse_loader.return_value = mock_instance
        
        result = get_loader(config)
        
        assert result is mock_instance
        mock_clickhouse_loader.assert_called_once_with(config)
    
    @patch('src.datax.core.factories.MongoLoader')
    def test_get_mongo_loader(self, mock_mongo_loader):
        """测试创建 MongoDB 加载器"""
        config = MongoConfig(
            kind=DatasourceKind.MONGODB,
            host="localhost",
            port=27017,
            database="test_db",
            username="test_user",
            password=SecretStr("test_password")
        )
        
        mock_instance = MagicMock()
        mock_mongo_loader.return_value = mock_instance
        
        result = get_loader(config)
        
        assert result is mock_instance
        mock_mongo_loader.assert_called_once_with(config)
    
    def test_get_loader_unsupported_kind(self):
        """测试不支持的数据源类型"""
        config = MySQLConfig(
            kind=DatasourceKind.MYSQL,
            host="localhost",
            port=3306,
            database="test_db",
            username="test_user",
            password=SecretStr("test_password")
        )
        # 手动设置为不支持的类型
        config.kind = "unsupported_type"  # type: ignore
        
        with pytest.raises(ValueError) as exc_info:
            get_loader(config)
        
        assert "Unsupported database kind for loader" in str(exc_info.value)


class TestGetTranslator:
    """get_translator 工厂函数测试"""
    
    def test_get_translator_none_config(self):
        """测试没有配置时返回 NoOpTranslator"""
        result = get_translator(None)
        
        assert isinstance(result, NoOpTranslator)
    
    def test_get_translator_noop_config(self):
        """测试 noop 配置返回 NoOpTranslator"""
        config = TranslatorConfig(kind="noop")
        
        result = get_translator(config)
        
        assert isinstance(result, NoOpTranslator)
    
    def test_get_translator_unsupported_kind(self):
        """测试不支持的转换器类型"""
        config = TranslatorConfig(kind="unsupported_translator")
        
        with pytest.raises(ValueError) as exc_info:
            get_translator(config)
        
        assert "Unsupported translator kind" in str(exc_info.value)
        assert "unsupported_translator" in str(exc_info.value)


class TestFactoriesIntegration:
    """工厂函数集成测试"""
    
    def test_factory_functions_logging(self):
        """测试工厂函数的日志记录"""
        with patch('src.datax.core.factories.logger') as mock_logger:
            # 测试提取器创建日志
            config = MySQLConfig(
                kind=DatasourceKind.MYSQL,
                host="localhost",
                port=3306,
                database="test_db",
                username="test_user",
                password=SecretStr("test_password")
            )
            
            with patch('src.datax.core.factories.MySQLExtractor'):
                get_extractor(config)
                mock_logger.info.assert_called_with(f"Creating extractor for kind: {config.kind}")
            
            # 测试加载器创建日志
            with patch('src.datax.core.factories.MySQLLoader'):
                get_loader(config)
                mock_logger.info.assert_called_with(f"Creating loader for kind: {config.kind}")
            
            # 测试转换器创建日志
            get_translator(None)
            mock_logger.info.assert_called_with("No translator config provided, using NoOpTranslator.")
            
            translator_config = TranslatorConfig(kind="noop")
            get_translator(translator_config)
            mock_logger.info.assert_called_with(f"Creating translator for kind: {translator_config.kind}")
    
    def test_all_database_kinds_supported(self):
        """测试所有数据库类型都被支持"""
        supported_kinds = [
            DatasourceKind.MYSQL,
            DatasourceKind.POSTGRES,
            DatasourceKind.CLICKHOUSE,
            DatasourceKind.MONGODB
        ]
        
        for kind in supported_kinds:
            # 为每种类型创建适当的配置
            if kind == DatasourceKind.MYSQL:
                config = MySQLConfig(
                    kind=kind, host="localhost", port=3306,
                    database="test", username="user", password=SecretStr("pass")
                )
            elif kind == DatasourceKind.POSTGRES:
                config = PostgresConfig(
                    kind=kind, host="localhost", port=5432,
                    database="test", username="user", password=SecretStr("pass")
                )
            elif kind == DatasourceKind.CLICKHOUSE:
                config = ClickHouseConfig(
                    kind=kind, host="localhost", port=8123,
                    database="test", username="user", password=SecretStr("pass")
                )
            elif kind == DatasourceKind.MONGODB:
                config = MongoConfig(
                    kind=kind, host="localhost", port=27017,
                    database="test", username="user", password=SecretStr("pass")
                )
            
            # 测试提取器和加载器工厂函数不会抛出异常
            with patch(f'src.datax.core.factories.{kind.value.title()}Extractor'):
                try:
                    get_extractor(config)
                except ValueError:
                    pytest.fail(f"get_extractor should support {kind}")
            
            with patch(f'src.datax.core.factories.{kind.value.title()}Loader'):
                try:
                    get_loader(config)
                except ValueError:
                    pytest.fail(f"get_loader should support {kind}")
