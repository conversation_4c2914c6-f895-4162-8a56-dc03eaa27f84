"""
加载器与元数据集成的单元测试。

测试更新后的加载器如何使用动态元数据查询来处理数据冲突，
替代之前的静态conflict_target配置。
"""

from unittest.mock import AsyncMock, MagicMock, patch

import pandas as pd
import pytest
from pydantic import SecretStr

from src.datax.connectors.mongo import MongoLoader
from src.datax.connectors.mysql import MySQLLoader
from src.datax.connectors.postgres import PostgresLoader
from src.datax.models import (
    ColumnInfo,
    ColumnType,
    ConflictStrategy,
    ImportConfig,
    IndexInfo,
    IndexType,
    MongoConfig,
    MySQLConfig,
    PostgresConfig,
    S3Config,
    TableMetadata,
)


class TestMySQLLoaderWithMetadata:
    """测试MySQL加载器与元数据集成"""

    @pytest.fixture
    def mysql_config(self):
        """MySQL配置fixture"""
        return MySQLConfig(
            host="localhost",
            port=3306,
            username="test_user",
            password=SecretStr("test_password"),
            database="test_db",
        )

    @pytest.fixture
    def mysql_loader(self, mysql_config):
        """MySQL加载器fixture"""
        return MySQLLoader(mysql_config)

    @pytest.fixture
    def sample_data(self):
        """示例数据fixture"""
        return pd.DataFrame(
            {"id": [1, 2, 3], "name": ["Alice", "Bob", "Charlie"], "age": [25, 30, 35]}
        )

    @pytest.fixture
    def s3_config(self):
        """S3配置fixture"""
        return S3Config(
            bucket="test-bucket",
            access_key="test-access-key",
            secret_key=SecretStr("test-secret-key"),
        )

    @pytest.fixture
    def import_config(self, s3_config):
        """导入配置fixture（不包含conflict_target）"""
        return ImportConfig(
            table_name="users",
            s3_source=s3_config,
            conflict_strategy=ConflictStrategy.IGNORE,
        )

    @pytest.mark.asyncio
    async def test_load_chunk_ignore_strategy(
        self, mysql_loader, sample_data, import_config
    ):
        """测试使用IGNORE策略加载数据"""
        with patch.object(mysql_loader, "_connect", new_callable=AsyncMock):
            with patch.object(mysql_loader, "connection") as mock_conn:
                mock_cursor = AsyncMock()
                mock_conn.cursor.return_value.__aenter__.return_value = mock_cursor
                mock_cursor.rowcount = 3
                # 设置异步方法
                mock_conn.commit = AsyncMock()
                mock_conn.rollback = AsyncMock()

                mysql_loader.connection = mock_conn

                await mysql_loader.load_chunk(sample_data, import_config)

                # 验证执行了INSERT IGNORE语句
                mock_cursor.executemany.assert_called_once()
                sql_call = mock_cursor.executemany.call_args[0][0]
                assert "INSERT IGNORE" in sql_call
                assert "users" in sql_call

    @pytest.mark.asyncio
    async def test_load_chunk_replace_strategy(
        self, mysql_loader, sample_data, import_config
    ):
        """测试使用REPLACE策略加载数据"""
        import_config.conflict_strategy = ConflictStrategy.REPLACE

        with patch.object(mysql_loader, "_connect", new_callable=AsyncMock):
            with patch.object(mysql_loader, "connection") as mock_conn:
                mock_cursor = AsyncMock()
                mock_conn.cursor.return_value.__aenter__.return_value = mock_cursor
                mock_cursor.rowcount = 3
                # 设置异步方法
                mock_conn.commit = AsyncMock()
                mock_conn.rollback = AsyncMock()

                mysql_loader.connection = mock_conn

                await mysql_loader.load_chunk(sample_data, import_config)

                # 验证执行了REPLACE INTO语句
                mock_cursor.executemany.assert_called_once()
                sql_call = mock_cursor.executemany.call_args[0][0]
                assert "REPLACE INTO" in sql_call
                assert "users" in sql_call


class TestPostgreSQLLoaderWithMetadata:
    """测试PostgreSQL加载器与元数据集成"""

    @pytest.fixture
    def postgres_config(self):
        """PostgreSQL配置fixture"""
        return PostgresConfig(
            host="localhost",
            port=5432,
            username="test_user",
            password=SecretStr("test_password"),
            database="test_db",
        )

    @pytest.fixture
    def postgres_loader(self, postgres_config):
        """PostgreSQL加载器fixture"""
        return PostgresLoader(postgres_config)

    @pytest.fixture
    def sample_data(self):
        """示例数据fixture"""
        return pd.DataFrame(
            {
                "id": [1, 2, 3],
                "name": ["Alice", "Bob", "Charlie"],
                "email": ["<EMAIL>", "<EMAIL>", "<EMAIL>"],
            }
        )

    @pytest.fixture
    def s3_config(self):
        """S3配置fixture"""
        return S3Config(
            bucket="test-bucket",
            access_key="test-access-key",
            secret_key=SecretStr("test-secret-key"),
        )

    @pytest.fixture
    def import_config(self, s3_config):
        """导入配置fixture"""
        return ImportConfig(
            table_name="users",
            s3_source=s3_config,
            conflict_strategy=ConflictStrategy.IGNORE,
        )

    @pytest.fixture
    def table_metadata(self):
        """表元数据fixture"""
        return TableMetadata(
            table_name="users",
            database_name="test_db",
            columns=[
                ColumnInfo(
                    name="id",
                    data_type=ColumnType.INTEGER,
                    is_primary_key=True,
                    is_nullable=False,
                ),
                ColumnInfo(
                    name="name", data_type=ColumnType.VARCHAR, is_nullable=False
                ),
                ColumnInfo(
                    name="email", data_type=ColumnType.VARCHAR, is_nullable=False
                ),
            ],
            indexes=[
                IndexInfo(
                    name="users_pkey",
                    index_type=IndexType.PRIMARY,
                    columns=["id"],
                    is_primary=True,
                    is_unique=True,
                )
            ],
            constraints=[],
        )

    @pytest.mark.asyncio
    async def test_load_chunk_with_metadata_ignore(
        self, postgres_loader, sample_data, import_config, table_metadata
    ):
        """测试使用元数据的IGNORE策略"""
        with patch.object(postgres_loader, "_connect", new_callable=AsyncMock):
            with patch.object(postgres_loader, "connection") as mock_conn:
                with patch(
                    "src.datax.connectors.postgres.PostgresMetadataProvider"
                ) as mock_provider_class:
                    # 模拟元数据提供者
                    mock_provider = AsyncMock()
                    mock_provider.get_table_metadata.return_value = table_metadata
                    mock_provider_class.return_value = mock_provider

                    # 设置异步方法
                    mock_conn.executemany = AsyncMock()

                    postgres_loader.connection = mock_conn

                    await postgres_loader.load_chunk(sample_data, import_config)

                    # 验证执行了ON CONFLICT DO NOTHING语句
                    mock_conn.executemany.assert_called_once()
                    sql_call = mock_conn.executemany.call_args[0][0]
                    assert "ON CONFLICT DO NOTHING" in sql_call

    @pytest.mark.asyncio
    async def test_load_chunk_with_metadata_upsert(
        self, postgres_loader, sample_data, import_config, table_metadata
    ):
        """测试使用元数据的UPSERT策略"""
        import_config.conflict_strategy = ConflictStrategy.UPSERT

        with patch.object(postgres_loader, "_connect", new_callable=AsyncMock):
            with patch.object(postgres_loader, "connection") as mock_conn:
                # 直接mock CachedMetadataProvider.get_table_metadata方法
                with patch(
                    "src.datax.core.metadata.CachedMetadataProvider.get_table_metadata"
                ) as mock_get_metadata:
                    mock_get_metadata.return_value = table_metadata

                    # 设置异步方法
                    mock_conn.executemany = AsyncMock()

                    postgres_loader.connection = mock_conn

                    await postgres_loader.load_chunk(sample_data, import_config)

                    # 验证执行了ON CONFLICT DO UPDATE语句
                    mock_conn.executemany.assert_called_once()
                    sql_call = mock_conn.executemany.call_args[0][0]
                    assert "ON CONFLICT" in sql_call
                    assert "DO UPDATE SET" in sql_call
                    assert '"id"' in sql_call  # 主键列

    @pytest.mark.asyncio
    async def test_load_chunk_metadata_failure_fallback(
        self, postgres_loader, sample_data, import_config
    ):
        """测试元数据查询失败时的回退机制"""
        with patch.object(postgres_loader, "_connect", new_callable=AsyncMock):
            with patch.object(postgres_loader, "connection") as mock_conn:
                with patch(
                    "src.datax.connectors.postgres.PostgresMetadataProvider"
                ) as mock_provider_class:
                    # 模拟元数据查询失败
                    mock_provider = AsyncMock()
                    mock_provider.get_table_metadata.side_effect = Exception(
                        "Database connection failed"
                    )
                    mock_provider_class.return_value = mock_provider

                    # 设置异步方法
                    mock_conn.executemany = AsyncMock()

                    postgres_loader.connection = mock_conn

                    await postgres_loader.load_chunk(sample_data, import_config)

                    # 验证回退到简单的ON CONFLICT DO NOTHING
                    mock_conn.executemany.assert_called_once()
                    sql_call = mock_conn.executemany.call_args[0][0]
                    assert "ON CONFLICT DO NOTHING" in sql_call


class TestMongoLoaderWithMetadata:
    """测试MongoDB加载器与元数据集成"""

    @pytest.fixture
    def mongo_config(self):
        """MongoDB配置fixture"""
        return MongoConfig(
            host="localhost",
            port=27017,
            username="test_user",
            password=SecretStr("test_password"),
            database="test_db",
        )

    @pytest.fixture
    def mongo_loader(self, mongo_config):
        """MongoDB加载器fixture"""
        return MongoLoader(mongo_config)

    @pytest.fixture
    def sample_data(self):
        """示例数据fixture"""
        return pd.DataFrame(
            {
                "_id": [
                    "507f1f77bcf86cd799439011",
                    "507f1f77bcf86cd799439012",
                    "507f1f77bcf86cd799439013",
                ],
                "name": ["Alice", "Bob", "Charlie"],
                "age": [25, 30, 35],
            }
        )

    @pytest.fixture
    def s3_config(self):
        """S3配置fixture"""
        return S3Config(
            bucket="test-bucket",
            access_key="test-access-key",
            secret_key=SecretStr("test-secret-key"),
        )

    @pytest.fixture
    def import_config(self, s3_config):
        """导入配置fixture"""
        return ImportConfig(
            table_name="users",
            s3_source=s3_config,
            conflict_strategy=ConflictStrategy.IGNORE,
        )

    @pytest.fixture
    def table_metadata(self):
        """MongoDB集合元数据fixture"""
        return TableMetadata(
            table_name="users",
            database_name="test_db",
            columns=[
                ColumnInfo(
                    name="_id",
                    data_type=ColumnType.VARCHAR,
                    is_primary_key=True,
                    is_unique=True,
                    is_nullable=False,
                ),
                ColumnInfo(name="name", data_type=ColumnType.VARCHAR, is_nullable=True),
                ColumnInfo(name="age", data_type=ColumnType.INTEGER, is_nullable=True),
            ],
            indexes=[
                IndexInfo(
                    name="_id_",
                    index_type=IndexType.PRIMARY,
                    columns=["_id"],
                    is_primary=True,
                    is_unique=True,
                )
            ],
            constraints=[],
        )

    @pytest.mark.asyncio
    async def test_load_chunk_ignore_strategy(
        self, mongo_loader, sample_data, import_config
    ):
        """测试MongoDB的IGNORE策略"""
        with patch.object(mongo_loader, "_connect", new_callable=AsyncMock):
            with patch.object(mongo_loader, "database") as mock_db:
                with patch.object(mongo_loader, "connection", new_callable=AsyncMock):
                    mock_collection = AsyncMock()
                    mock_result = MagicMock()
                    mock_result.inserted_ids = ["id1", "id2", "id3"]
                    mock_collection.insert_many.return_value = mock_result
                    mock_db.__getitem__.return_value = mock_collection

                    mongo_loader.database = mock_db

                    await mongo_loader.load_chunk(sample_data, import_config)

                # 验证调用了insert_many
                mock_collection.insert_many.assert_called_once()
                call_args = mock_collection.insert_many.call_args
                assert call_args[1]["ordered"] is False  # 应该使用无序插入

    @pytest.mark.asyncio
    async def test_load_chunk_upsert_strategy(
        self, mongo_loader, sample_data, import_config, table_metadata
    ):
        """测试MongoDB的UPSERT策略"""
        import_config.conflict_strategy = ConflictStrategy.UPSERT

        with patch.object(mongo_loader, "_connect", new_callable=AsyncMock):
            with patch.object(mongo_loader, "database") as mock_db:
                with patch.object(mongo_loader, "connection", new_callable=AsyncMock):
                    # 直接mock CachedMetadataProvider.get_table_metadata方法
                    with patch(
                        "src.datax.core.metadata.CachedMetadataProvider.get_table_metadata"
                    ) as mock_get_metadata:
                        mock_get_metadata.return_value = table_metadata

                        mock_collection = AsyncMock()
                        mock_result = MagicMock()
                        mock_result.upserted_count = 2
                        mock_result.modified_count = 1
                        mock_collection.bulk_write.return_value = mock_result
                        mock_db.__getitem__.return_value = mock_collection

                        mongo_loader.database = mock_db

                        await mongo_loader.load_chunk(sample_data, import_config)

                        # 验证调用了bulk_write进行upsert
                        mock_collection.bulk_write.assert_called_once()
                        operations = mock_collection.bulk_write.call_args[0][0]

                    # 验证操作类型
                    assert len(operations) == 3  # 三条记录
                    for op in operations:
                        assert "replaceOne" in op
                        assert op["replaceOne"]["upsert"] is True
