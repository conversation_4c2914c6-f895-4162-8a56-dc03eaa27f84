"""
元数据查询提供者的单元测试。

测试各种数据库的元数据查询功能，包括：
- MySQL元数据查询
- PostgreSQL元数据查询
- ClickHouse元数据查询
- MongoDB元数据查询
- 元数据缓存功能
"""

from unittest.mock import AsyncMock, patch

import pytest
from pydantic import SecretStr

from src.datax.connectors.clickhouse import <PERSON>lick<PERSON>ouseMetadataProvider
from src.datax.connectors.mongo import MongoMetadataProvider
from src.datax.connectors.mysql import MySQLMetadataProvider
from src.datax.connectors.postgres import PostgresMetadataProvider
from src.datax.core.metadata import CachedMetadataProvider, MetadataCache
from src.datax.models import (
    ClickHouseConfig,
    ColumnInfo,
    ColumnType,
    MongoConfig,
    MySQLConfig,
    PostgresConfig,
    TableMetadata,
)


class TestMetadataCache:
    """测试元数据缓存功能"""

    def test_cache_initialization(self):
        """测试缓存初始化"""
        cache = MetadataCache(max_size=50)
        assert cache.size() == 0
        assert cache._max_size == 50

    def test_cache_put_and_get(self):
        """测试缓存存取功能"""
        cache = MetadataCache(max_size=10)

        # 创建测试元数据
        metadata = TableMetadata(
            table_name="test_table",
            database_name="test_db",
            columns=[
                ColumnInfo(
                    name="id",
                    data_type=ColumnType.INTEGER,
                    is_primary_key=True,
                    is_nullable=False,
                )
            ],
            indexes=[],
            constraints=[],
        )

        # 测试存储和获取
        cache.put(metadata, schema_name="test_schema", database_name="test_db")
        retrieved = cache.get("test_table", "test_schema", "test_db")

        assert retrieved is not None
        assert retrieved.table_name == "test_table"
        assert len(retrieved.columns) == 1
        assert retrieved.columns[0].name == "id"

    def test_cache_lru_eviction(self):
        """测试LRU淘汰机制"""
        cache = MetadataCache(max_size=2)

        # 创建三个不同的元数据
        for i in range(3):
            metadata = TableMetadata(
                table_name=f"table_{i}",
                database_name="test_db",
                columns=[],
                indexes=[],
                constraints=[],
            )
            cache.put(metadata, database_name="test_db")

        # 第一个应该被淘汰
        assert cache.get("table_0", database_name="test_db") is None
        assert cache.get("table_1", database_name="test_db") is not None
        assert cache.get("table_2", database_name="test_db") is not None
        assert cache.size() == 2

    def test_cache_clear(self):
        """测试缓存清空"""
        cache = MetadataCache()

        metadata = TableMetadata(
            table_name="test_table",
            database_name="test_db",
            columns=[],
            indexes=[],
            constraints=[],
        )

        cache.put(metadata, database_name="test_db")
        assert cache.size() == 1

        cache.clear()
        assert cache.size() == 0
        assert cache.get("test_table", database_name="test_db") is None


class TestMySQLMetadataProvider:
    """测试MySQL元数据查询提供者"""

    @pytest.fixture
    def mysql_config(self):
        """MySQL配置fixture"""
        return MySQLConfig(
            host="localhost",
            port=3306,
            username="test_user",
            password=SecretStr("test_password"),
            database="test_db",
        )

    @pytest.fixture
    def mysql_provider(self, mysql_config):
        """MySQL元数据提供者fixture"""
        return MySQLMetadataProvider(mysql_config)

    @pytest.mark.asyncio
    async def test_get_database_name(self, mysql_provider):
        """测试获取数据库名称"""
        database_name = await mysql_provider.get_database_name()
        assert database_name == "test_db"

    @pytest.mark.asyncio
    async def test_table_exists_mock(self, mysql_provider):
        """测试表存在性检查（模拟）"""
        with patch.object(mysql_provider, "_connect", new_callable=AsyncMock):
            with patch.object(mysql_provider, "connection") as mock_conn:
                mock_cursor = AsyncMock()
                mock_cursor.fetchone.return_value = (1,)
                mock_conn.cursor.return_value.__aenter__.return_value = mock_cursor

                mysql_provider.connection = mock_conn

                result = await mysql_provider.table_exists("test_table")
                assert result is True

                mock_cursor.execute.assert_called_once()


class TestPostgreSQLMetadataProvider:
    """测试PostgreSQL元数据查询提供者"""

    @pytest.fixture
    def postgres_config(self):
        """PostgreSQL配置fixture"""
        return PostgresConfig(
            host="localhost",
            port=5432,
            username="test_user",
            password=SecretStr("test_password"),
            database="test_db",
        )

    @pytest.fixture
    def postgres_provider(self, postgres_config):
        """PostgreSQL元数据提供者fixture"""
        return PostgresMetadataProvider(postgres_config)

    @pytest.mark.asyncio
    async def test_get_database_name(self, postgres_provider):
        """测试获取数据库名称"""
        database_name = await postgres_provider.get_database_name()
        assert database_name == "test_db"


class TestClickHouseMetadataProvider:
    """测试ClickHouse元数据查询提供者"""

    @pytest.fixture
    def clickhouse_config(self):
        """ClickHouse配置fixture"""
        return ClickHouseConfig(
            host="localhost",
            port=8123,
            username="default",
            password=SecretStr(""),
            database="test_db",
        )

    @pytest.fixture
    def clickhouse_provider(self, clickhouse_config):
        """ClickHouse元数据提供者fixture"""
        return ClickHouseMetadataProvider(clickhouse_config)

    @pytest.mark.asyncio
    async def test_get_database_name(self, clickhouse_provider):
        """测试获取数据库名称"""
        database_name = await clickhouse_provider.get_database_name()
        assert database_name == "test_db"


class TestMongoMetadataProvider:
    """测试MongoDB元数据查询提供者"""

    @pytest.fixture
    def mongo_config(self):
        """MongoDB配置fixture"""
        return MongoConfig(
            host="localhost",
            port=27017,
            username="test_user",
            password=SecretStr("test_password"),
            database="test_db",
        )

    @pytest.fixture
    def mongo_provider(self, mongo_config):
        """MongoDB元数据提供者fixture"""
        return MongoMetadataProvider(mongo_config)

    @pytest.mark.asyncio
    async def test_get_database_name(self, mongo_provider):
        """测试获取数据库名称"""
        database_name = await mongo_provider.get_database_name()
        assert database_name == "test_db"

    def test_infer_column_type_from_value(self, mongo_provider):
        """测试从值推断列类型"""
        assert mongo_provider._infer_column_type_from_value(True) == ColumnType.BOOLEAN
        assert mongo_provider._infer_column_type_from_value(42) == ColumnType.INTEGER
        assert mongo_provider._infer_column_type_from_value(3.14) == ColumnType.DOUBLE
        assert (
            mongo_provider._infer_column_type_from_value("test") == ColumnType.VARCHAR
        )
        assert (
            mongo_provider._infer_column_type_from_value({"key": "value"})
            == ColumnType.JSON
        )
        assert (
            mongo_provider._infer_column_type_from_value([1, 2, 3]) == ColumnType.JSON
        )


class TestCachedMetadataProvider:
    """测试带缓存的元数据查询提供者"""

    @pytest.fixture
    def mock_provider(self):
        """模拟的元数据提供者"""
        provider = AsyncMock()
        provider.get_database_name.return_value = "test_db"
        return provider

    @pytest.fixture
    def cached_provider(self, mock_provider):
        """带缓存的元数据提供者"""
        return CachedMetadataProvider(mock_provider, cache_size=10)

    @pytest.mark.asyncio
    async def test_cache_hit(self, cached_provider, mock_provider):
        """测试缓存命中"""
        # 创建测试元数据
        metadata = TableMetadata(
            table_name="test_table",
            database_name="test_db",
            columns=[],
            indexes=[],
            constraints=[],
        )

        # 第一次调用，应该查询数据库
        mock_provider.get_table_metadata.return_value = metadata
        result1 = await cached_provider.get_table_metadata("test_table")

        # 第二次调用，应该从缓存获取
        result2 = await cached_provider.get_table_metadata("test_table")

        # 验证结果相同
        assert result1.table_name == result2.table_name

        # 验证只调用了一次数据库查询
        mock_provider.get_table_metadata.assert_called_once()

    @pytest.mark.asyncio
    async def test_cache_miss(self, cached_provider, mock_provider):
        """测试缓存未命中"""
        metadata = TableMetadata(
            table_name="test_table",
            database_name="test_db",
            columns=[],
            indexes=[],
            constraints=[],
        )

        mock_provider.get_table_metadata.return_value = metadata

        # 查询不同的表，应该都查询数据库
        await cached_provider.get_table_metadata("table1")
        await cached_provider.get_table_metadata("table2")

        # 验证调用了两次数据库查询
        assert mock_provider.get_table_metadata.call_count == 2
