import os
from collections.abc import AsyncGenerator

import aioboto3
import pytest
import pytest_asyncio
from aioch import Client
from motor.motor_asyncio import AsyncIOMotorClient
from pydantic import SecretStr
from sqlalchemy.ext.asyncio import AsyncEngine, create_async_engine
from testcontainers.clickhouse import C<PERSON><PERSON>ouse<PERSON>ontainer
from testcontainers.minio import MinioContainer
from testcontainers.mongodb import MongoDbContainer
from testcontainers.mysql import MySqlContainer
from testcontainers.postgres import PostgresContainer

from src.datax.models import ClickHouseConfig


def setup_docker_host():
    """
    动态检测docker.sock位置并设置DOCKER_HOST环境变量。
    支持多个操作系统平台，包括macOS、Linux和Windows。
    """
    # 常见的docker.sock位置
    docker_sock_paths = [
        # macOS (Docker Desktop)
        os.path.expanduser("~/.docker/run/docker.sock"),
        # Linux (标准位置)
        "/var/run/docker.sock",
        # Linux (用户级Docker)
        os.path.expanduser("~/.docker/run/docker.sock"),
        # Windows WSL2 (通过WSL访问)
        "/var/run/docker.sock",
    ]

    # 检查哪个路径存在
    for path in docker_sock_paths:
        if os.path.exists(path):
            docker_sock = f"unix://{path}"
            os.environ["DOCKER_HOST"] = docker_sock
            print(f"Docker socket found at: {path}")
            return

    # 如果没有找到docker.sock，尝试使用默认的Docker Desktop设置
    # 这通常适用于macOS和Windows
    default_docker_host = "tcp://localhost:2375"
    os.environ["DOCKER_HOST"] = default_docker_host
    print(f"Docker socket not found, using default: {default_docker_host}")


# 在模块导入时执行一次
setup_docker_host()


def _has_marker_in_session(session: pytest.Session, marker_name: str) -> bool:
    """
    检查当前测试会话中是否有任何测试用例使用了指定的标记。
    用于 session scope 的 fixture 来决定是否需要启动对应的容器。
    """
    for item in session.items:
        if item.get_closest_marker(marker_name):
            return True
    return False


@pytest_asyncio.fixture(scope="session")
async def postgres_engine(
    request: pytest.FixtureRequest,
) -> AsyncGenerator[AsyncEngine]:
    """
    基于 PostgreSQL 容器，创建一个异步 SQLAlchemy 引擎。
    使用 asyncpg 驱动进行异步数据库操作。
    """
    # 检查当前测试会话中是否有使用 postgres 标记的测试
    if not _has_marker_in_session(request.session, "postgres"):
        pytest.skip("No tests marked with @pytest.mark.postgres in this session")

    with PostgresContainer("postgres:17-alpine") as container:
        # 将同步 URL 转换为异步 URL (使用 asyncpg)
        sync_url = container.get_connection_url()
        print(f"Original URL: {sync_url}")
        # testcontainers 返回的 URL 可能包含 psycopg2 驱动，需要替换
        if "postgresql+psycopg2://" in sync_url:
            async_url = sync_url.replace(
                "postgresql+psycopg2://", "postgresql+asyncpg://"
            )
        else:
            async_url = sync_url.replace("postgresql://", "postgresql+asyncpg://")
        print(f"Async URL: {async_url}")

        engine = create_async_engine(async_url)
        yield engine
        await engine.dispose()


@pytest_asyncio.fixture(scope="session")
async def mysql_engine(request: pytest.FixtureRequest) -> AsyncGenerator[AsyncEngine]:
    """
    基于 MySQL 容器，创建一个异步 SQLAlchemy 引擎。
    使用 asyncmy 驱动进行异步数据库操作。
    """
    # 检查当前测试会话中是否有使用 mysql 标记的测试
    if not _has_marker_in_session(request.session, "mysql"):
        pytest.skip("No tests marked with @pytest.mark.mysql in this session")

    with MySqlContainer("mysql:8") as container:
        container.with_bind_ports(3306, 3306)
        # 将同步 URL 转换为异步 URL (使用 asyncmy)
        sync_url = container.get_connection_url()
        # testcontainers 可能返回不同格式的 URL，需要处理多种情况
        if "mysql+pymysql://" in sync_url:
            async_url = sync_url.replace("mysql+pymysql://", "mysql+asyncmy://")
        elif "mysql://" in sync_url:
            async_url = sync_url.replace("mysql://", "mysql+asyncmy://")
        else:
            # 如果是其他格式，强制使用 asyncmy
            async_url = sync_url.replace("mysql+", "mysql+asyncmy+", 1)
        engine = create_async_engine(async_url)
        yield engine
        await engine.dispose()


@pytest.fixture(scope="session")
def clickhouse_container_info(request: pytest.FixtureRequest):
    """
    基于 ClickHouse 容器，提供连接信息。
    这是一个同步 fixture，避免异步事件循环问题。
    """
    # 检查当前测试会话中是否有使用 clickhouse 标记的测试
    if not _has_marker_in_session(request.session, "clickhouse"):
        pytest.skip("No tests marked with @pytest.mark.clickhouse in this session")

    with ClickHouseContainer("clickhouse/clickhouse-server:25.1-alpine") as container:
        url = container.get_connection_url()
        http_port = container.get_exposed_port(8123)
        yield {"url": url, "http_port": http_port}


@pytest.fixture(scope="function")
def clickhouse_config(clickhouse_container_info: dict) -> ClickHouseConfig:
    """
    基于 ClickHouse 容器信息，创建 ClickHouse 配置对象。
    """
    # 解析容器 URL 来获取连接参数
    # clickhouse_container_url 格式类似: clickhouse://test:test@localhost:55020/test
    from urllib.parse import urlparse

    parsed = urlparse(clickhouse_container_info["url"])

    password = parsed.password or ""
    return ClickHouseConfig(
        host=parsed.hostname or "localhost",
        port=clickhouse_container_info["http_port"],  # 使用HTTP端口
        username=parsed.username or "default",
        password=SecretStr(password) if password else None,
        database=parsed.path.lstrip("/") or "default",
    )


@pytest_asyncio.fixture(scope="function")
async def clickhouse_client(clickhouse_container_info: dict) -> AsyncGenerator[Client]:
    """
    基于 ClickHouse 容器信息，创建一个异步客户端。
    使用 aioch 驱动进行异步数据库操作。
    """
    client = Client.from_url(clickhouse_container_info["url"])
    yield client


@pytest.fixture(scope="session")
def mongodb_container_url(request: pytest.FixtureRequest):
    """
    基于 MongoDB 容器，提供连接 URL。
    这是一个同步 fixture，避免异步事件循环问题。
    """
    # 检查当前测试会话中是否有使用 mongodb 标记的测试
    if not _has_marker_in_session(request.session, "mongodb"):
        pytest.skip("No tests marked with @pytest.mark.mongodb in this session")

    with MongoDbContainer("mongo:8") as container:
        connection_url = container.get_connection_url()
        yield connection_url


@pytest_asyncio.fixture(scope="function")
async def mongodb_client(
    mongodb_container_url: str,
) -> AsyncGenerator[AsyncIOMotorClient]:
    """
    基于 MongoDB 容器 URL，创建一个异步客户端。
    使用 motor 驱动进行异步数据库操作。
    """
    client = AsyncIOMotorClient(mongodb_container_url)

    # 测试连接
    await client.admin.command("ping")

    yield client
    client.close()


@pytest.fixture(scope="session")
def minio_container_config(request: pytest.FixtureRequest):
    """
    基于 MinIO 容器，提供连接配置。
    这是一个同步 fixture，避免异步事件循环问题。
    """
    # 检查当前测试会话中是否有使用 minio 标记的测试
    if not _has_marker_in_session(request.session, "minio"):
        pytest.skip("No tests marked with @pytest.mark.minio in this session")

    with MinioContainer("minio/minio:latest") as container:
        # 获取 MinIO 配置
        config = container.get_config()
        host = container.get_container_host_ip()
        port = container.get_exposed_port(9000)
        endpoint_url = f"http://{host}:{port}"

        yield {
            "endpoint_url": endpoint_url,
            "aws_access_key_id": config["access_key"],
            "aws_secret_access_key": config["secret_key"],
        }


@pytest_asyncio.fixture(scope="function")
async def minio_client(
    minio_container_config: dict,
) -> AsyncGenerator[aioboto3.Session.client]:  # type: ignore
    """
    基于 MinIO 容器配置，创建一个异步 S3 客户端。
    使用 aioboto3 进行异步 S3 操作。
    """
    # 创建异步 S3 客户端
    session = aioboto3.Session()
    client = await session.client(
        "s3",
        endpoint_url=minio_container_config["endpoint_url"],
        aws_access_key_id=minio_container_config["aws_access_key_id"],
        aws_secret_access_key=minio_container_config["aws_secret_access_key"],
        use_ssl=False,
    ).__aenter__()

    yield client
    await client.__aexit__(None, None, None)
